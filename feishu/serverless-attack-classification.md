# 基于研究命题的Serverless攻击分类：输入扰动与行为偏离映射

## 🎯 研究命题导向分类框架

### 1.1 核心研究命题

**命题**：Serverless函数在输入扰动下的行为偏离模式分析

**研究问题**：
- RQ1：不同类型的输入扰动如何触发Serverless函数的异常行为？
- RQ2：异常行为在日志层面表现出哪些可检测的偏离模式？
- RQ3：如何建立输入扰动→技术缺陷→行为后果的映射关系？

### 1.2 基于变异测试的攻击触发分类

#### 维度A：输入扰动类型（攻击触发机制）
| 扰动类别 | 技术实现 | 变异策略 | 检测指标 |
|----------|----------|----------|----------|
| **边界值扰动** | 参数极值测试 | 最大值、最小值、空值 | 异常处理路径 |
| **格式扰动** | 数据结构变异 | JSON/XML格式破坏 | 解析错误日志 |
| **权限扰动** | IAM边界测试 | 权限提升/降级 | 访问拒绝日志 |
| **时序扰动** | 异步调用延迟 | 超时、重排序 | 调用链时间戳 |

#### 维度B：行为偏离模式（攻击后果检测）
| 偏离类型 | 日志特征 | 偏离度量 | 检测方法 |
|----------|----------|----------|----------|
| **调用链偏离** | 函数执行序列异常 | 调用图编辑距离 | 图对比算法 |
| **资源访问偏离** | 非预期资源调用 | 资源访问熵值 | 访问模式分析 |
| **性能偏离** | 响应时间异常 | 时间序列异常检测 | 统计检验 |
| **错误处理偏离** | 异常日志级别 | 错误类型分布 | 日志聚类 |

## 🔍 维度混淆案例分析（研究视角）

### 2.1 混淆案例：DVSA反序列化漏洞

#### 原始混淆（维度混合）
```
❌ 错误并行分类：
├── 反序列化漏洞（技术类型）
├── 任意代码执行（攻击后果）
├── 第三方库漏洞（漏洞来源）
└── 使用已知漏洞组件（引入方式）

问题：四个不同维度被并列列出
```

#### 研究命题下的重新分类
```
✅ 研究导向分类：
├── 输入扰动：构造恶意序列化payload（触发机制）
├── 技术缺陷：Apache Commons Collections Gadget链（缺陷载体）
├── 行为偏离：正常订单处理 → 异常系统命令执行（后果检测）
└── 日志特征：exec调用出现在Lambda日志中（检测信号）
```

### 2.2 混淆案例：权限配置错误

#### 原始混淆（维度混合）
```
❌ 错误并行分类：
├── 权限配置错误（配置问题）
├── 数据泄露（安全后果）
├── S3桶权限配置（具体场景）
└── 公开访问（实现方式）

问题：配置-后果-场景-实现混为一谈
```

#### 研究命题下的重新分类
```
✅ 研究导向分类：
├── 输入扰动：PUT请求到公开S3 bucket（触发动作）
├── 技术缺陷：Terraform模板中public-read-write权限（配置缺陷）
├── 行为偏离：正常文件上传 → 触发非预期Lambda处理（流程偏离）
└── 日志特征：Lambda函数异常调用模式（检测指标）
```

## 🧪 实验验证框架

### 3.1 变异测试实验设计

#### 实验1：输入扰动与行为偏离映射
```
实验目标：验证不同输入扰动类型对应的行为偏离模式

测试场景：
├── 边界值扰动：price=-1触发异常处理
├── 格式扰动：损坏的JSON触发解析错误
├── 权限扰动：越权S3访问触发拒绝日志
└── 时序扰动：并发请求触发竞态条件

检测指标：
├── 调用链长度变化
├── 资源访问模式偏离
├── 错误日志类型分布
└── 性能指标异常
```

#### 实验2：日志行为偏离量化
```
偏离度量方法：
├── 调用图编辑距离：D(G_normal, G_abnormal)
├── 资源访问熵值：H(access_pattern)
├── 时间序列异常：|t_actual - t_expected| > threshold
└── 错误类型聚类：k-means on error logs

验证假设：
H1: 恶意输入扰动显著增加调用链复杂度 (p<0.05)
H2: 权限滥用扰动显著改变资源访问模式 (p<0.01)
H3: 时序扰动显著影响函数响应时间分布 (p<0.05)
```

### 3.2 数据收集与分析

#### 日志数据源
| 数据源 | 日志类型 | 采集方式 | 分析重点 |
|--------|----------|----------|----------|
| **CloudWatch** | Lambda执行日志 | AWS SDK | 调用链、错误信息 |
| **S3 Access Log** | 存储访问日志 | Bucket配置 | 资源访问模式 |
| **API Gateway** | 请求日志 | CloudWatch集成 | 输入参数、响应状态 |
| **X-Ray** | 分布式追踪 | SDK集成 | 完整调用链 |

#### 分析方法
```
1. 基线建立：收集1000次正常调用日志
2. 扰动注入：对每个测试用例注入特定扰动
3. 偏离检测：计算正常vs异常的行为差异
4. 模式识别：使用聚类算法识别偏离模式
5. 映射验证：建立扰动-偏离的对应关系
```

## 📊 分类维度正交化矩阵

### 4.1 输入扰动 × 行为偏离映射表

| 输入扰动类型 | 调用链偏离 | 资源访问偏离 | 性能偏离 | 错误处理偏离 |
|--------------|------------|--------------|----------|--------------|
| **边界值扰动** | 异常分支激活 | 边界检查资源调用 | 验证延迟增加 | 验证错误日志 |
| **格式扰动** | 解析错误路径 | 重试机制触发 | 解析开销增大 | 格式错误异常 |
| **权限扰动** | 权限检查失败 | 访问拒绝记录 | 权限验证延迟 | 权限错误日志 |
| **时序扰动** | 竞态条件路径 | 并发资源竞争 | 等待时间延长 | 超时错误日志 |

### 4.2 技术缺陷 × 检测特征矩阵

| 技术缺陷类别 | 日志检测特征 | 监控指标 | 告警规则 |
|--------------|--------------|----------|----------|
| **输入验证缺陷** | 异常参数日志 | 验证失败率 | 错误率>5% |
| **权限控制缺陷** | 访问拒绝日志 | 权限失败次数 | 连续失败>3次 |
| **资源控制缺陷** | 资源访问异常 | 资源使用量 | 超出预期范围 |
| **逻辑缺陷** | 业务逻辑错误 | 业务流程异常 | 关键路径偏离 |

## 🔧 工具与实现

### 5.1 变异测试工具链

#### 扰动注入器
```python
class InputMutator:
    def __init__(self, baseline_requests):
        self.baseline = baseline_requests
        
    def boundary_mutation(self, param_name, min_val, max_val):
        """边界值扰动"""
        return [min_val-1, min_val, max_val, max_val+1, None]
    
    def format_mutation(self, json_data):
        """格式扰动：损坏JSON结构"""
        return [
            json_data.replace('"', ''),  # 移除引号
            json_data[:-1],  # 截断JSON
            json_data.replace(':', ';'),  # 错误分隔符
        ]
    
    def permission_mutation(self, original_role):
        """权限扰动：修改IAM角色"""
        return ["*", "arn:aws:s3:::*", "arn:aws:dynamodb:*"]
```

#### 行为偏离检测器
```python
class BehaviorAnalyzer:
    def __init__(self, normal_logs):
        self.baseline = self.parse_logs(normal_logs)
        
    def detect_deviation(self, test_logs):
        """检测行为偏离"""
        test_data = self.parse_logs(test_logs)
        
        deviations = {
            'call_chain': self.calculate_call_chain_distance(test_data),
            'resource_access': self.calculate_access_entropy(test_data),
            'performance': self.detect_performance_anomaly(test_data),
            'errors': self.cluster_error_patterns(test_data)
        }
        return deviations
```

### 5.2 实验数据管理

#### 目录结构
```
mutate-dis/
├── experiments/
│   ├── data/
│   │   ├── baseline/          # 正常行为基线数据
│   │   ├── mutations/       # 扰动测试数据
│   │   └── results/         # 实验结果
│   ├── scripts/
│   │   ├── mutator.py       # 扰动注入脚本
│   │   ├── analyzer.py      # 行为分析脚本
│   │   └── visualizer.py    # 结果可视化
│   └── notebooks/
│       ├── dvsa_analysis.ipynb
│       └── permission_test.ipynb
```

## 📝 迁移计划

### 6.1 文档迁移
1. 将0830.md中的实验部分迁移到本框架
2. 建立实验数据与分类体系的映射关系
3. 更新研究计划与实验设计

### 6.2 代码迁移
1. 提取0830.md中的测试用例
2. 重新组织为研究命题导向的实验
3. 建立数据收集与分析流水线

---

## 🎯 总结

本分类体系基于研究命题重新组织Serverless攻击分析：
1. **解决维度混淆**：将来源-类型-触发-后果正交化分离
2. **突出研究重点**：聚焦输入扰动与行为偏离的映射关系
3. **支持实验验证**：提供可量化的偏离检测方法
4. **工具化实现**：配套完整的测试与分析工具链

该框架直接服务于变异测试研究，为理解Serverless函数在恶意输入下的行为模式提供系统化方法。