# 变异-稳定性理论基础

## 核心假设

### 稳定性假设
> **良性行为在变异后更容易回归到稳定状态，而恶意行为对变异更加敏感**

这个假设基于以下观察：
1. 良性系统通常具有容错机制和鲁棒性
2. 恶意行为往往依赖特定条件，变异容易破坏
3. 攻击载荷经过精心构造，微小改动影响大

### 理论依据

#### 1. 系统稳定性理论
- **李雅普诺夫稳定性**: 系统在扰动后回到平衡状态的能力
- **鲁棒控制理论**: 系统对不确定性和扰动的容忍度
- **容错系统设计**: 良性系统设计时考虑异常处理

#### 2. 攻击脆弱性理论  
- **精确依赖性**: 攻击成功依赖精确的触发条件
- **载荷敏感性**: 恶意载荷对字符修改极其敏感
- **隐蔽性要求**: 攻击需要绕过检测，变异可能暴露

## 形式化定义

### 基本概念

#### 样本空间
- **S**: 样本空间，包含所有可能的输入样本
- **L**: 良性样本子集，L ⊆ S  
- **M**: 恶意样本子集，M ⊆ S
- **L ∩ M = ∅**, **L ∪ M ⊆ S**

#### 变异函数
- **μ: S → S**: 变异函数，将样本映射到变异体
- **Μ = {μ₁, μ₂, ..., μₙ}**: 变异函数集合
- **μᵢ(x)**: 对样本x应用变异函数μᵢ的结果

#### 行为函数
- **β: S → B**: 行为提取函数，将样本映射到行为空间
- **B**: 行为空间，包含所有可能的行为特征
- **β(x)**: 样本x的行为表示

#### 相似度函数
- **σ: B × B → [0,1]**: 行为相似度函数
- **σ(b₁, b₂) = 1** 当且仅当 b₁ = b₂
- **σ(b₁, b₂) = 0** 当且仅当 b₁, b₂ 完全不同

### 稳定性定义

#### 单点稳定性
对于样本x和变异函数μ，定义稳定性为：

$S(x, \mu) = \sigma(\beta(x), \beta(\mu(x)))$

#### 多变异稳定性
对于样本$x$和变异函数集合$M$，定义稳定性为：

$S(x, M) = \frac{1}{|M|} \sum_{\mu_i \in M} S(x, \mu_i)$

#### 类别稳定性
对于样本集合$C$和变异函数集合$M$，定义稳定性为：

$S(C, M) = \frac{1}{|C|} \sum_{x_i \in C} S(x_i, M)$

### 稳定性假设的数学表达

#### 主要假设
对于足够大的变异函数集合$M$：

$E[S(L, M)] > E[S(M, M)] + \delta$

其中$\delta > 0$是显著性阈值。

#### 分布假设
良性样本的稳定性分布和恶意样本的稳定性分布满足：

$P(S(x, M) > \theta \mid x \in L) > P(S(x, M) > \theta \mid x \in M)$

对于适当选择的阈值$\theta$。

## 适用性条件分析

### 良性样本的必要条件

#### 1. 结构完整性
良性样本应具有：
- **语法正确性**: 符合格式规范
- **逻辑一致性**: 内部逻辑无矛盾  
- **完整性**: 包含必要的组件

#### 2. 容错性
良性样本应表现出：
- **输入容忍**: 对输入变异有一定容忍度
- **优雅降级**: 异常情况下优雅处理
- **恢复能力**: 能从扰动中恢复

#### 3. 鲁棒性
良性样本应具备：
- **噪声抗性**: 对随机噪声不敏感
- **变异抗性**: 核心功能对变异稳定
- **一致性**: 相似输入产生相似输出

### 恶意样本的充分条件

#### 1. 精确依赖性
恶意样本通常：
- **条件苛刻**: 需要精确的触发条件
- **参数敏感**: 关键参数不能随意修改
- **顺序依赖**: 执行顺序不能改变

#### 2. 隐蔽性要求
恶意样本需要：
- **伪装性**: 表面看起来正常
- **规避性**: 避免被检测系统发现
- **变异敏感**: 修改可能暴露恶意意图

#### 3. 脆弱性
恶意样本表现出：
- **载荷脆弱**: 攻击载荷易被破坏
- **结构脆弱**: 攻击结构不稳定
- **功能脆弱**: 攻击功能易失效

### 假设失效的情况

#### 1. 高度混淆的良性样本
- **过度优化**: 极端优化导致脆弱
- **复杂依赖**: 复杂的内部依赖关系
- **边界情况**: 处于设计边界的样本

#### 2. 鲁棒的恶意样本
- **多态攻击**: 具有多种表现形式
- **自适应攻击**: 能适应环境变化
- **冗余攻击**: 包含冗余的攻击路径

#### 3. 特殊场景
- **实时系统**: 时间约束下的系统
- **资源受限**: 计算资源极其有限
- **特殊协议**: 非标准的通信协议

## 变异策略理论

### 变异策略分类

#### 1. 语法保持变异 (Syntax-Preserving)
**目标**: 保持样本的语法正确性
**原理**: 在语法规则约束下进行变异
**适用**: 需要确保变异后样本可执行

**数学定义**:
```
μₛ(x) ∈ S_valid
其中 S_valid 是语法正确的样本集合
```

#### 2. 语义保持变异 (Semantic-Preserving)  
**目标**: 保持样本的核心语义
**原理**: 功能等价的替换和调整
**适用**: 区分功能差异和恶意行为

**数学定义**:
```
semantic(μₛₑₘ(x)) ≈ semantic(x)
其中 semantic() 是语义提取函数
```

#### 3. 对抗性变异 (Adversarial)
**目标**: 专门破坏恶意特征
**原理**: 针对性破坏攻击载荷
**适用**: 确认攻击行为的敏感性

**数学定义**:
```
μₐdᵥ(x) = argmax_μ |β(x) - β(μ(x))|
针对恶意样本x
```

### 变异效果预测

#### 变异影响模型
对于变异函数μ和样本x：

$\text{Impact}(\mu, x) = \|\beta(x) - \beta(\mu(x))\|$

#### 最优变异选择
选择变异函数使得：

$\mu^* = \arg\max_{\mu} |\text{Impact}(\mu, x_{\text{malicious}}) - \text{Impact}(\mu, x_{\text{benign}})|$

## 稳定性度量理论

### 相似度度量方法

#### 1. 执行结果相似度
```
σₑₓₑc(r₁, r₂) = {
    1, if crash(r₁) = crash(r₂)
    0, otherwise
}
```

#### 2. 序列相似度
```
σₛₑq(s₁, s₂) = 1 - EditDistance(s₁, s₂) / max(|s₁|, |s₂|)
```

#### 3. 特征向量相似度
```
σfₑₐₜ(f₁, f₂) = cosine_similarity(f₁, f₂)
```

### 聚合稳定性度量

#### 加权平均
```
S_weighted(x, Μ) = Σ wᵢ × S(x, μᵢ) / Σ wᵢ
其中 wᵢ 是变异函数μᵢ的权重
```

#### 分位数稳定性
```
S_quantile(x, Μ) = Q_p({S(x, μᵢ) | μᵢ ∈ Μ})
其中 Q_p 是p分位数函数
```

## 分类理论

### 阈值学习

#### 贝叶斯最优阈值
```
θ* = argmin_θ P(error | θ)
其中 P(error | θ) = P(FP | θ) + P(FN | θ)
```

#### ROC最优阈值
```
θ* = argmax_θ (TPR(θ) - FPR(θ))
```

### 置信度估计

#### 基于距离的置信度
```
Confidence(x) = min(|S(x, Μ) - θ_benign|, |S(x, Μ) - θ_malicious|)
```

#### 基于概率的置信度
```
Confidence(x) = max(P(benign | S(x, Μ)), P(malicious | S(x, Μ)))
```

这个理论框架为变异-稳定性异常检测提供了坚实的数学基础，明确了假设的适用条件，并为后续的实现和实验提供了理论指导。