# Serverless/FaaS 攻击方式补充与分类优化

## 执行摘要

基于现有FaaS攻击文档的深度分析，发现当前分类存在以下局限性：
- 过度依赖传统Web安全分类框架
- 缺乏对Serverless架构特性的针对性分析
- 未充分考虑云原生环境下的攻击路径

本补充文档提出**基于攻击路径的Serverless攻击分类法**，从攻击者视角重构攻击面认知。

## 新型攻击分类体系：基于攻击路径的Serverless攻击矩阵

### 1. 事件驱动攻击面 (Event-Driven Attack Surface)

#### 1.1 事件源污染攻击
- **API Gateway事件注入**：通过API Gateway参数污染触发恶意执行
- **对象存储事件操控**：伪造S3 PUT/DELETE事件触发未授权函数调用
- **消息队列事件篡改**：通过SQS/SNS消息格式漏洞绕过安全检查
- **定时事件劫持**：修改CloudWatch Events规则触发恶意调度

#### 1.2 事件数据解析攻击
- **多格式事件解析漏洞**：利用Lambda对不同事件格式的解析差异
- **事件schema验证绕过**：通过畸形JSON/XML结构绕过输入验证
- **事件大小限制绕过**：利用分片上传机制绕过payload大小限制

### 2. 函数生命周期攻击面 (Function Lifecycle Attack Surface)

#### 2.1 冷启动攻击
- **容器复用攻击**：利用容器复用机制在函数实例间植入恶意代码
- **初始化代码注入**：在函数初始化阶段注入恶意依赖或配置
- **环境变量泄露**：通过冷启动日志泄露敏感环境变量

#### 2.2 执行时攻击
- **内存限制绕过**：利用内存管理漏洞突破函数内存限制
- **超时机制绕过**：通过心跳机制保持恶意函数长时间运行
- **并发限制耗尽**：通过并发调用耗尽账户并发配额

### 3. 权限配置攻击面 (Permission Configuration Attack Surface)

#### 3.1 IAM权限利用
- **过度权限函数滥用**：利用过度授权的函数访问其他云服务
- **角色链攻击**：通过函数角色获取临时凭证横向移动
- **跨账户权限提升**：利用跨账户角色配置错误实现权限提升

#### 3.2 资源访问控制绕过
- **存储桶权限滥用**：利用宽松的S3 bucket policy访问敏感数据
- **数据库权限绕过**：通过函数绕过DynamoDB/RDS的访问控制
- **密钥管理服务滥用**：利用KMS密钥权限解密敏感数据

### 4. 依赖供应链攻击面 (Dependency Supply Chain Attack Surface)

#### 4.1 第三方库攻击
- **恶意依赖包植入**：在npm/pip包中植入恶意代码
- **依赖混淆攻击**：利用私有仓库命名冲突植入恶意包
- **版本锁定绕过**：通过依赖版本范围控制实现恶意更新

#### 4.2 容器镜像攻击
- **基础镜像污染**：在官方基础镜像中植入后门
- **层缓存投毒**：利用Docker层缓存机制植入恶意文件
- **镜像仓库劫持**：通过镜像仓库权限控制实现镜像替换

### 5. 网络攻击面 (Network Attack Surface)

#### 5.1 VPC配置攻击
- **VPC逃逸**：利用VPC配置错误访问内部网络资源
- **安全组绕过**：通过函数网络配置绕过安全组限制
- **NAT Gateway滥用**：利用NAT Gateway进行隐蔽通信

#### 5.2 API调用链攻击
- **服务链劫持**：通过篡改API调用链实现中间人攻击
- **DNS劫持**：利用DNS配置错误重定向API调用
- **证书验证绕过**：通过函数环境绕过SSL证书验证

### 6. 监控与日志攻击面 (Monitoring & Logging Attack Surface)

#### 6.1 日志注入攻击
- **CloudWatch日志污染**：通过函数输出注入虚假日志
- **日志访问权限滥用**：利用宽松的日志访问权限查看敏感信息
- **日志保留策略绕过**：通过日志生命周期管理绕过审计

#### 6.2 监控指标操控
- **CloudWatch指标欺骗**：通过恶意函数调用伪造监控指标
- **告警机制绕过**：通过指标操控绕过安全告警
- **成本监控欺骗**：通过资源使用模式操控成本监控

## 遗漏的关键攻击方式

### 7. 云原生特定攻击

#### 7.1 容器运行时攻击
- **容器逃逸到主机**：利用容器运行时漏洞逃逸到底层主机
- **容器间通信嗅探**：通过共享网络命名空间嗅探其他容器流量
- **容器文件系统逃逸**：通过挂载配置访问主机文件系统

#### 7.2 Kubernetes特定攻击
- **Pod安全策略绕过**：利用PSP配置错误运行特权容器
- **Service Account滥用**：通过Kubernetes Service Account获取集群权限
- **ConfigMap/Secret泄露**：通过函数访问Kubernetes ConfigMap和Secret

### 8. 计费与资源攻击

#### 8.1 计费欺诈攻击
- **资源耗尽攻击**：通过大量调用耗尽账户资源配额
- **成本放大攻击**：通过特定调用模式大幅增加云费用
- **免费额度滥用**：滥用云服务免费额度进行攻击

#### 8.2 资源竞争攻击
- **冷启动频率操控**：通过调用模式操控冷启动频率影响性能
- **资源预留攻击**：通过并发设置影响其他函数的资源分配
- **区域资源耗尽**：在特定区域耗尽资源影响服务可用性

## 攻击场景矩阵

| 攻击路径 | 触发条件 | 影响范围 | 检测难度 | 防御优先级 |
|---------|----------|----------|----------|------------|
| 事件源污染 | 外部输入可控 | 单个函数 | 低 | 高 |
| 权限滥用 | IAM配置错误 | 整个账户 | 中 | 高 |
| 供应链投毒 | 依赖更新 | 所有使用函数 | 高 | 中 |
| 容器逃逸 | 运行时漏洞 | 底层基础设施 | 高 | 高 |
| 计费欺诈 | API访问权限 | 财务成本 | 低 | 中 |

## 防御策略建议

### 1. 架构层面防御
- **零信任架构**：对所有函数调用进行身份验证和授权
- **最小权限原则**：为每个函数配置最小必要的IAM权限
- **网络隔离**：使用VPC和私有子网隔离函数网络访问

### 2. 运行时防御
- **函数沙箱强化**：使用gVisor等沙箱技术隔离函数执行环境
- **资源限制**：为每个函数设置严格的内存、时间和并发限制
- **实时监控**：部署实时异常检测和行为分析系统

### 3. 供应链安全
- **依赖扫描**：对所有第三方依赖进行安全扫描和版本锁定
- **镜像签名**：使用容器镜像签名验证镜像完整性
- **私有仓库**：使用私有依赖仓库控制第三方包来源

### 4. 事件验证
- **事件schema验证**：对所有事件输入进行严格的schema验证
- **签名验证**：对关键事件进行数字签名验证
- **频率限制**：对事件触发频率进行限制防止滥用

## 检测与响应框架

### 1. 检测指标
- **异常调用模式**：检测异常的函数调用时间和频率
- **权限使用异常**：监控IAM权限的实际使用情况
- **资源使用异常**：监控CPU、内存、网络等资源使用模式

### 2. 响应机制
- **自动隔离**：检测到攻击时自动隔离可疑函数
- **访问撤销**：自动撤销可疑的IAM权限和访问密钥
- **事件溯源**：记录详细的事件日志用于事后分析

## 结论

传统的Web安全分类已无法完全覆盖Serverless架构的安全风险。基于攻击路径的新分类体系能够更好地识别和防御云原生环境下的安全威胁。建议采用分层防御策略，从架构设计、运行时保护、供应链安全和事件验证四个维度构建完整的Serverless安全防护体系。