# 扰动-稳定性文献搜索策略指南

## 核心调研目标
> **重点调研**: 扰动前后产生或者不产生变化的场景和工作

## 搜索策略层次结构

### 第一层：核心概念构建

#### 1.1 主要概念词汇库
```
稳定性概念:
- stability, robustness, resilience, invariance, tolerance
- consistency, preservation, persistence, resistance

变异概念:
- mutation, perturbation, variation, modification, transformation
- fuzzing, injection, alteration, disturbance, noise

行为概念:
- behavior, execution, response, output, performance
- functionality, semantics, properties, characteristics
```

#### 1.2 核心搜索模式
```
模式A: [稳定性词] + [变异词] + [领域词]
- "behavioral stability" + "under mutation" + "software"
- "execution robustness" + "perturbation" + "program"

模式B: [对比概念] + [变异词]
- "sensitive vs robust" + "mutation"
- "fragile vs stable" + "perturbation"
- "brittle vs resilient" + "input variation"

模式C: [保持/破坏概念] + [变异词]
- "behavior preservation" + "under perturbation"
- "property invariance" + "program mutation"
- "semantic equivalence" + "input modification"
```

### 第二层：应用场景特化搜索

#### 2.1 二进制程序分析领域
```
核心关键词组合:
- "program mutation" + "crash analysis" + ["stability", "robustness", "resilience"]
- "input fuzzing" + "behavior consistency"
- "vulnerability detection" + "mutation" + "robustness"
- "fault injection" + "program stability"
- "counterfactual analysis" + "software" + "mutation"

具体搜索组合:
- "benign program" + "mutation robust*"
- "vulnerable program" + "mutation sensitiv*"
- "crash avoidance" + "input perturbation"
- "execution path" + "stability analysis"
- "program repair" + "mutation testing"
- "buffer overflow" + "input mutation" + "robustness"
- "memory corruption" + "perturbation" + "resilience"

高价值论文识别模式:
- "fuzzing" + "mutation" + "program analysis" + ["stability", "robustness"]
- "software testing" + "perturbation" + "behavior"
- "program synthesis" + "mutation" + "correctness"
```

#### 2.2 Web应用安全领域
```
核心关键词组合:
- "web attack" + "parameter mutation" + ["detection", "resilience", "robustness"]
- "payload" + "perturbation" + ["stability", "robustness", "analysis"]
- "request transformation" + "behavior analysis"
- "HTTP fuzzing" + "response consistency"
- "web application" + "mutation" + "security"

具体搜索组合:
- "SQL injection" + "mutation" + ["evasion", "robustness", "stability"]
- "XSS payload" + "perturbation" + "robustness"
- "attack payload" + "stability" + "analysis"
- "legitimate request" + "parameter variation"
- "WAF evasion" + "input transformation"
- "web security" + "request mutation" + "detection"
- "parameter fuzzing" + "behavioral analysis"

特殊关注方向:
- "payload stability" + "mutation"
- "attack robustness" + "parameter perturbation"
- "web application" + "behavior preservation" + "parameter changes"
```

#### 2.3 协议和网络安全领域
```
核心关键词组合:
- "protocol fuzzing" + "state consistency"
- "network mutation" + "behavior analysis"
- "packet perturbation" + "protocol stability"
- "protocol implementation" + "mutation testing"

具体搜索组合:
- "network protocol" + "robustness analysis"
- "packet modification" + "behavior impact"
- "protocol state machine" + "mutation"
- "network security" + "perturbation analysis"
- "communication protocol" + "mutation" + "resilience"
```

### 第三层：方法论和理论搜索

#### 3.1 测试和分析方法
```
方法关键词:
- "mutation testing" + ["stability", "robustness", "invariant", "property"]
- "perturbation analysis" + ["software", "security", "program", "system"]
- "sensitivity analysis" + ["input", "parameter", "behavior", "execution"]
- "robustness testing" + ["mutation", "variation", "perturbation", "noise"]
- "invariant checking" + ["program", "execution", "behavior", "property"]

组合搜索:
- "differential testing" + "mutation"
- "metamorphic testing" + "stability"
- "property-based testing" + "perturbation"
- "equivalence checking" + "program mutation"
- "automated testing" + "behavior preservation"
- "test case generation" + "mutation" + "robustness"
```

#### 3.2 理论基础搜索
```
理论概念:
- "program equivalence" + "mutation"
- "behavioral semantics" + "perturbation"
- "execution model" + "stability"
- "formal verification" + "mutation testing"
- "program analysis" + "perturbation theory"

搜索组合:
- "semantic equivalence" + "under mutation"
- "behavioral bisimulation" + "program"
- "execution trace" + "similarity" + "mutation"
- "program synthesis" + "robustness"
- "correctness preservation" + "program transformation"
- "functional equivalence" + "input perturbation"
```

### 第四层：高级搜索技巧

#### 4.1 布尔搜索模式
```
模式A: 核心概念交集
(("mutation" OR "perturbation" OR "variation") AND ("stability" OR "robustness" OR "invariance"))

模式B: 对比分析
(("benign" OR "legitimate" OR "normal") AND ("malicious" OR "attack" OR "vulnerable") AND ("mutation" OR "perturbation"))

模式C: 行为保持
(("behavior" OR "execution" OR "response") AND ("preserv*" OR "maintain*" OR "invariant") AND ("mutation" OR "perturbation"))

模式D: 敏感性分析
(("sensitive" OR "robust" OR "fragile" OR "stable") AND ("mutation" OR "perturbation") AND ("analysis" OR "testing"))
```

#### 4.2 否定搜索（排除不相关）
```
生物学排除:
-"gene mutation" -"genetic" -"biological" -"DNA" -"medical" -"cancer" -"organism"

硬件排除:
-"hardware mutation" -"radiation" -"physical" -"device" -"chip" -"circuit"

进化算法排除（除非相关）:
-"evolutionary algorithm" -"genetic programming" (除非与程序进化相关)

自然语言处理排除:
-"language model" -"NLP" -"text" (除非与代码分析相关)
```

#### 4.3 时间和质量过滤
```
时间范围:
- 2015..2024 (近期工作优先)
- 2020..2024 (最新发展)

会议质量过滤:
高质量安全会议: (USENIX Security OR CCS OR NDSS OR "IEEE S&P")
高质量系统会议: (SOSP OR OSDI OR ATC OR EuroSys)
高质量软工会议: (ICSE OR FSE OR ASE OR ISSTA OR OOPSLA)

引用过滤:
- cited by > 5 (确保最低影响力)
- cited by > 20 (高影响力工作)
```

## 特殊关注的研究问题

### 核心问题导向搜索

#### Q1: 什么时候扰动影响大？
```
搜索词组合:
- "mutation impact" + "factor*" + ["analysis", "prediction", "measurement"]
- "perturbation effect" + "predict*" + ["model", "analysis", "factors"]
- "sensitivity" + "analysis" + ["program", "software", "system"]
- "vulnerability" + "mutation" + "sensitivity"
- "attack" + "perturbation" + "robustness"

特殊关注:
- "high sensitivity" + "mutation"
- "fragile" + "perturbation"
- "brittle" + "input changes"
```

#### Q2: 什么时候扰动影响小？
```
搜索词组合:
- "mutation neutral*" + ["program", "behavior", "execution"]
- "perturbation toleran*" + ["system", "application", "software"]
- "robust* under mutation" + ["analysis", "testing", "verification"]
- "invariant" + "perturbation" + ["property", "behavior", "execution"]
- "stable" + "mutation" + ["behavior", "execution", "performance"]

特殊关注:
- "low sensitivity" + "mutation"
- "resilient" + "perturbation"
- "stable" + "input variation"
```

#### Q3: 如何预测扰动效果？
```
搜索词组合:
- "mutation effect" + "predict*" + ["model", "analysis", "framework"]
- "perturbation impact" + "model*" + ["prediction", "analysis", "estimation"]
- "sensitivity metric*" + ["mutation", "perturbation", "robustness"]
- "robustness measure*" + ["mutation", "perturbation", "testing"]

特殊关注:
- "predictive model" + "mutation"
- "sensitivity estimation" + "perturbation"
- "robustness metric" + "software"
```

## 文献筛选策略

### 初筛标准
1. **标题相关性**: 包含核心概念组合
2. **摘要明确性**: 明确提到行为稳定性或变异影响
3. **实证研究**: 有具体实验数据支持
4. **时效性**: 近5年发表优先

### 深度筛选标准
1. **实验数据**: 有具体的稳定性/敏感性量化数据
2. **对比分析**: 提供了不同条件下的行为对比
3. **方法论**: 描述了具体的变异策略和评估方法
4. **案例分析**: 包含成功/失败案例的原因分析
5. **直接相关性**: 与您的研究问题直接相关(评分4-5)

### 文献管理模板
```
论文基本信息:
- 标题: 
- 作者: 
- 会议/期刊: 
- 年份: 
- 引用数: 

核心贡献:
- 主要方法: 
- 关键发现: 
- 实验数据: 

与本研究关系:
- 相关性评分: (1-5)
- 支持/挑战假设: 
- 可借鉴方法: 
- 局限性: 

后续行动:
- 需要深入阅读: [是/否]
- 重点关注章节: 
- 相关工作追踪: 
```

这个搜索策略指南为您的文献调研提供了系统性的方法，特别针对"扰动前后产生或不产生变化"的研究需求进行了优化设计。