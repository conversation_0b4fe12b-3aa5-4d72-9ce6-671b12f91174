- 这个文件夹中是我当前的研究项目。它出于一种直觉：变异时，良性行为更稳定。我的目的是探讨这个命题在不同场景上（单主机/分布式/serverless，漏洞挖掘/异常检测，编译型程序/解释型程序，输入是文件/网络请求等等）的成立范围。其中“变异”的操作是我可以选定的，我可以选择适用于该场景的策略来让这个命题尽量成立。
### 目录结构
- feishu是新的目录，其余的目录都是之前生成的，内容不一定准确，很可能有AI杜撰的成分。你可以进行检查和删改完善，确保关于我命题的部分清晰准确无歧义，并对我的命题充分分析。
- feishu目录包含我自己的记录（self.md前半段，比较零散的部分）和我与同事整理的serverless攻击（几个formatted文档）
- 0830.md是我这两天需要完成的汇报文档，我需要汇报

### 目前要做的
1. 我发现serverless攻击分类存在一定问题，现请你参照rca/feishu目录下以faas开头的文件，检查其中是否存在攻击手段、缺陷类型与攻击后果混合表述的情况（比如使用包含漏洞的库，与反序列化漏洞就不应该并列）。针对此问题，请在rca/feishu/faas-attacks-supplement.md中阐述你的理解及应对方案。本次梳理的核心目的是建立一套系统化的Serverless攻击体系，以应对和阐释现有分类标准（如OWASP、PureSec）在不同层次概念上存在的模糊性（例如缺陷、手段、后果等）。具体分类方式可根据实际情况进行完善，需开展进一步全面考量。
2. 在建立了一套完善的serverless attack分类体系后（重要的是让我明晰已有分类标准中的模糊之处，并便于我后续整理在serverless attack方面我的命题的成立范围）

### 一些规则
1. 永远不要编造任何内容，要确保一切内容有依据。当你提供推测的时候，需要注明推测。同理，不要编造任何数据。
2. 用中文回答我