- intuition：变异时，良性行为更容易回归/更稳定。对于crash/non-crash就是像racing这样的，对于web场景则是对请求体进行变异，从而触发漏洞或者执行正常请求（这方面的理解可以参照amber，但amber是我和同事目前的研究，不确保准确）。你可以注意到在amber中，我们通过扰动后请求与原请求的差异来判断是恶意还是良性，其依据就是injection漏洞的payload容易被破坏。
  - 现在我的毕业课题就是研究这一intuition在不同场景下的适用范围，我可以尽量完善自己的模型来让它成立，当然这不可能对所有情况都成立。
  - 具体来说，对于漏洞挖掘采用的指标就是crash，对于异常检测，可能使用日志（deeplog：日志输入LSTM？）/溯源图/状态机。
  
这种intuition在不同场景下的适用性。
我的想法：可能的变量有很多，包括mutate的方法、应用场景、输入、检测指标等。
每种idea都存在一定问题，重要的是成立的范围。看起来打算就这个题目了，“不要畏难”？半年还是一年两年？
对于漏洞挖掘采用的指标就是crash，对于异常检测，可能使用日志（deeplog：日志输入LSTM？）/溯源图/状态机。可能的

8.29
- 扰动前后产生或者不产生变化的场景和工作
- “变异测试”？
- 师兄说“二维”（漏洞挖掘、异常检测/web 单主机），而我感受到了漏洞、手段和和场景本身的耦合
先从现有的工作开始，serverless异常检测哪些场景不支持（都大幅度变化）；RACING单主机vul挖掘，哪些可以哪些不可以。然后再看其他的，web fuzz异常检测，单主机异常检测。分布式系统。
师兄不希望push我，希望我当成自己的任务，自己规划。要动手做实事
- 是否还有异常检测/漏洞挖掘外的其他任务，也要再考虑
- RACING和amber不一样。师兄：那就不一样，可以调整我们的直觉到对应的场景中。不要想太复杂，预设很多阻碍。师兄还是很有信心能完成的，可能是“无论如何，总有些场景是成立的”
- 漏洞和攻击不一样。师兄：确实不一样，所以是不同的场景。实际上我的疑问在于amber用实验结果来支撑这一intuition，但实际实验时对一种漏洞只用了一种利己的攻击方法，不同攻击方法可能效果并不通——不过这似乎又到了“度量距离”的细节上了
再找模糊测试相关的，看是否适配

## 📋 关键内容系统总结

### 🎯 核心研究直觉的本质
**变异稳定性假设**：良性行为在变异操作下表现出更强的稳定性，这种差异可作为区分良性与恶意行为的依据。核心洞察来源于：
- **Amber项目经验**：injection漏洞的payload容易被破坏，扰动后请求与原请求的差异可区分恶意/良性
- **RACING项目验证**：crash作为漏洞挖掘指标，验证该直觉在二进制程序中的适用性

### 🎓 毕业课题精确定位
**核心研究问题**：变异稳定性假设在不同**程序输入类型**下的适用范围和边界条件

**输入类型二分法**：
- **文件输入场景**：RACING类二进制程序，输入为文件格式（如可执行文件、配置文件）
- **结构化请求场景**：Web服务、微服务、Serverless，输入为HTTP请求、API调用等结构化数据

### 🔬 多维度变量体系

#### 关键变量交互矩阵
| 变量维度 | 文件输入场景 | 结构化请求场景 | 研究价值 |
|----------|--------------|----------------|----------|
| **变异方法** | 字节级变异、语法保持变异 | 语义保持变异、协议级变异 | 输入格式对变异策略的影响 |
| **检测指标** | crash/no-crash | 日志异常、响应差异、状态变化 | 不同输入类型的有效检测手段 |
| **应用场景** | 漏洞挖掘 | 异常检测、安全防护 | 直觉的跨域适用性验证 |

#### 技术实现路径
- **漏洞挖掘**：crash作为ground truth，验证稳定性假设
- **异常检测**：日志(LSTM)/溯源图/状态机等多维度验证
- **Web场景**：需根据输入类型而非应用场景分类

### 🚧 研究边界与约束

#### 明确排除范围
- **分布式系统**：暂不涉及节点间交互复杂性
- **特定攻击方法**：不预设攻击方式限制，关注行为稳定性本质
- **度量细节**：避免过早陷入"距离度量"的技术细节

#### 场景归属判断标准
- **Web场景**：根据输入是文件上传还是API请求判断归属
- **单主机**：作为基础验证环境，控制变量
- **Serverless**：作为结构化请求的典型代表场景

### 📊 项目一致性深度验证

| 验证维度 | 一致性分析 | 具体体现 |
|----------|------------|----------|
| **核心假设** | ✅完全一致 | 变异稳定性假设贯穿mutate-dis项目始终 |
| **技术路径** | ✅精准匹配 | 从RACING的文件输入到Serverless的结构化请求 |
| **验证方法** | ✅多元兼容 | 支持crash、日志、响应等多维度检测 |
| **场景扩展** | ✅自然演进 | 从单一二进制到云原生服务的合理扩展 |

### 🎯 四阶段后续行动计划

#### 阶段1：实验场景选择与基线建立（2-3周）
- **文件输入**：选择RACING中3-5个代表性程序
- **结构化请求**：选择Serverless平台（OpenFaaS、AWS Lambda）的2-3个典型函数
- **基线数据**：收集每类场景1000+良性样本和已知恶意样本

#### 阶段2：实验设计与变异策略制定（3-4周）
- **文件输入**：设计字节级、语法级、语义级变异算子
- **结构化请求**：设计协议级、参数级、逻辑级变异策略
- **对照实验**：设置无变异、随机变异、对抗变异对照组

#### 阶段3：数据收集与稳定性量化（4-6周）
- **稳定性指标**：定义行为稳定性量化方法（响应一致性、错误率、状态保持度）
- **数据收集**：每类场景收集5000+变异实验数据
- **统计分析**：使用统计检验验证稳定性差异显著性

#### 阶段4：结果验证与边界探索（3-4周）
- **假设验证**：验证稳定性假设在不同输入类型下的成立性
- **边界探索**：发现假设失效的场景和条件
- **模型优化**：基于验证结果调整和完善理论模型

### ⚠️ 风险识别与应对策略

#### 主要风险点
1. **数据获取困难**：Serverless场景恶意样本不足
   - **应对**：使用已知漏洞函数+人工构造恶意变体
2. **变异算子设计**：缺乏结构化请求变异经验
   - **应对**：参考RESTler等API模糊测试工具
3. **稳定性度量**：跨场景度量标准不统一
   - **应对**：定义场景特定的稳定性指标，保持核心概念一致

#### 时间规划与里程碑
- **总周期**：12-16周（3-4个月）
- **月度检查点**：每月评估进展，调整实验设计
- **关键里程碑**：
  - 第4周：完成实验环境搭建
  - 第8周：完成初步数据收集
  - 第12周：完成主要实验验证
  - 第16周：完成论文撰写与结果分析

### 🔄 持续迭代机制
- **每周进展记录**：更新实验日志和发现
- **月度深度复盘**：评估假设有效性，调整研究方向
- **师兄定期沟通**：每2-3周进行进展汇报和技术讨论
- **文献持续跟踪**：关注相关领域最新研究进展