# Serverless漏洞与攻击面系统总结

## 📊 漏洞分类与映射

### OWASP TOP-10 映射表

| 编号 | 分类 | 相关内容 |
|------|------|----------|
| 1 | 注入 | OWASP-1, puresec-1, AngularJS的注入 |
| 2 | 认证和身份管理 | OWASP-2, puresec-2 |
| 3 | 访问控制与权限配置 | OWASP-5, puresec-4, CVE-2025-3048 |
| 4 | 敏感数据与密钥存储 | OWASP-3, puresec-7 |
| 5 | 配置与部署安全 | OWASP-6, puresec-3 |
| 6 | 第三方组件与依赖 | OWASP-9, puresec-6, CVE-2023-46129 |
| 7 | 日志与监控 | OWASP-10, puresec-5 |
| 8 | 拒绝服务/资源耗尽 | puresec-8, ReDOS, CVE-2019-11253 |
| 9 | 不安全反序列化 | OWASP-8 |
| 10 | 函数调用流/控制流操控 | puresec-9 |
| 11 | 错误处理与异常信息 | puresec-10 |
| 12 | Web特定威胁（XXE, XSS等） | OWASP-4, OWASP-7 |

## 🔍 关键发现

### 通用 vs 特化漏洞
- **通用漏洞**: 属于编程语言或第三方库，跨框架普遍存在
- **特化漏洞**: 特定于Serverless框架，体现Serverless架构特色

### Serverless安全资源汇总
- **Awesome Serverless Security**: https://github.com/puresec/awesome-serverless-security

## 🎯 Function Injection详解

Function Injection实际包含多种注入类型，与传统Injection攻击类似：

### 注入类型清单
- **操作系统命令注入** (OS command injection)
- **函数运行时代码注入** (Function runtime code injection)
  - Node.js/JavaScript
  - Python
  - Java
  - C#
  - Golang
- **SQL注入** (SQL injection)
- **NoSQL注入** (NoSQL injection)
- **发布/订阅消息数据篡改** (Pub/Sub Message Data Tampering)
  - MQTT数据注入
- **对象反序列化攻击** (Object deserialization attacks)
- **XML外部实体攻击** (XML External Entity - XXE)
- **服务器端请求伪造** (Server-Side Request Forgery - SSRF)

## 📋 标准框架对比

### OWASP 2017 Serverless Top-10
- **S1:2017** - Injection
- **S2:2017** - Broken Authentication
- **S3:2017** - Sensitive Data Exposure
- **S4:2017** - XML External Entities (XXE)
- **S5:2017** - Broken Access Control
- **S6:2017** - Security Misconfiguration
- **S7:2017** - Cross-Site Scripting (XSS)
- **S8:2017** - Insecure Deserialization
- **S9:2017** - Using Components with Known Vulnerabilities
- **S10:2017** - Insufficient Logging and Monitoring

### Puresec SAS Top-10
- **SAS-1** - Function Event Data Injection
- **SAS-2** - Broken Authentication
- **SAS-3** - Insecure Serverless Deployment Configuration
- **SAS-4** - Over-Privileged Function Permissions & Roles
- **SAS-5** - Inadequate Function Monitoring and Logging
- **SAS-6** - Insecure 3rd Party Dependencies
- **SAS-7** - Insecure Application Secrets Storage
- **SAS-8** - Denial of Service & Financial Resource Exhaustion
- **SAS-9** - Serverless Function Execution Flow Manipulation
- **SAS-10** - Improper Exception Handling and Verbose Error Messages

## 🔧 OpenFaaS CVE分析

### CVE-2019-11253
- **问题**: YAML解析导致资源耗尽（Kubernetes相关）

### CVE-2023-46129
- **问题**: 第三方库nkeys使用固定密钥进行所有加密

### CVE-2025-3048
- **问题**: 符号链接复制到本地

### CVE-2025-3047
- **问题**: 容器内利用符号链接访问主机敏感文件

### 其他相关漏洞
- **Sonatype-2016-0064、CVE-2019-10768、CVE-2020-7676** - 第三方框架AngularJS的注入漏洞
- **CVE-2022-28391等** - 旧的Alpine镜像包含的若干漏洞（Docker相关）

## 🎯 Serverless特定攻击面

### 1. 事件驱动架构攻击
- 事件注入与伪造
- 消息队列投毒
- 触发器滥用

### 2. 权限边界突破
- 函数权限过度授予
- 跨函数调用滥用
- 云服务权限横向移动

### 3. 冷启动攻击
- 容器复用攻击
- 共享资源嗅探
- 初始化代码注入

### 4. 计费与资源攻击
- 函数调用次数滥用
- 执行时间耗尽
- 内存资源耗尽

## 📊 风险评估矩阵

| 攻击类型 | 发生概率 | 影响程度 | 检测难度 | 综合风险 |
|----------|----------|----------|----------|----------|
| 注入攻击 | 高 | 中-高 | 中 | **高** |
| 权限滥用 | 中 | 高 | 高 | **高** |
| 资源耗尽 | 高 | 中 | 低 | **中** |
| 信息泄露 | 中 | 中 | 中 | **中** |
| 控制流操控 | 低-中 | 高 | 高 | **中-高** |