# 基于变异稳定性的异常检测研究

## 项目概述

本项目研究核心假设："**良性行为更容易回归/更稳定**"在不同异常检测场景下的适用性，并建立通用的变异-稳定性分析框架。

## 核心Intuition

### 观察与假设
1. **Racing场景**：良性程序变异后更容易回到正常状态，漏洞程序难以维持崩溃条件
2. **Amber场景**：良性Web请求具有稳定业务逻辑，攻击请求的payload易被破坏
3. **通用原理**：良性行为具有内在稳定性和容错性，恶意行为更加脆弱

### 研究目标
- 分析"良性行为更稳定"假设的适用条件和边界
- 建立通用的变异-稳定性分析框架
- 在多个场景验证方法有效性

## 应用场景

### 1. 漏洞挖掘 (Crash-based)
- **指标**: 程序crash/non-crash状态
- **变异**: 指令级、函数级、数据级变异
- **稳定性**: 良性程序变异后不易崩溃
- **参考**: Racing论文的强化学习根因分析

### 2. Web异常检测 (Behavior-based)  
- **指标**: 日志模式、HTTP响应
- **变异**: 参数替换、载荷扰动
- **稳定性**: 良性请求变异后行为相似
- **参考**: Amber论文的请求扰动分析

### 3. 日志异常检测 (Pattern-based)
- **指标**: 日志序列模式
- **变异**: 事件序列扰动、时间调整
- **稳定性**: 正常流程对变异具有容错性
- **参考**: DeepLog等日志分析方法

### 4. 状态机异常检测 (State-based)
- **指标**: 状态转换序列
- **变异**: 状态跳跃、事件注入
- **稳定性**: 正常状态机路径稳定

## 技术框架

### 变异策略
1. **语法级变异**: 保持语法正确性
2. **语义级变异**: 保持核心功能不变  
3. **对抗性变异**: 专门破坏恶意载荷

### 稳定性度量
1. **执行结果稳定性**: crash率、错误码分布
2. **行为序列稳定性**: 编辑距离、序列相似度
3. **特征空间稳定性**: 向量距离、聚类性质

## 项目结构

```
mutate-dis/
├── README.md              # 项目概览
├── docs/                  # 文档
│   ├── research-plan.md   # 研究计划
│   ├── theory.md         # 理论基础
│   └── implementation.md  # 实现方案
├── framework/            # 核心框架
│   ├── mutation/         # 变异引擎
│   ├── execution/        # 执行引擎
│   ├── analysis/         # 分析引擎
│   └── classification/   # 分类器
├── scenarios/            # 应用场景
│   ├── binary/          # 二进制分析
│   ├── web/             # Web请求分析
│   ├── logs/            # 日志分析
│   └── state/           # 状态机分析
├── experiments/          # 实验代码
│   ├── datasets/        # 数据集
│   ├── baselines/       # 基准方法
│   └── evaluation/      # 评估脚本
└── papers/              # 相关论文参考
```

## 创新点

1. **理论创新**: 首次系统分析变异-稳定性假设适用边界
2. **方法创新**: 多层次变异策略和多维度稳定性度量
3. **框架创新**: 通用的跨场景异常检测框架
4. **实用价值**: 提高检测精度，降低误报率

## 参考文献

- Racing: 基于强化学习的漏洞根因分析
- Amber: 基于扰动的Web异常检测  
- DeepLog: 基于深度学习的日志异常检测
- 其他相关异常检测和变异测试工作