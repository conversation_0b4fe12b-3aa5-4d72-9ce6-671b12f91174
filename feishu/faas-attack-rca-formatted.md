# FaaS攻击根因分析报告

## 📋 调研目的

调研真实的FaaS攻击场景（主要来源于故意设计的脆弱应用），加以分类，并分析每种攻击场景是否适合进行根因分析（这里根因分析指找到"根因代码行"）。

## 🔄 攻击分类重构

### 分类问题的发现

#### 现有分类的交叉问题
在OWASP Serverless Top 10等现有分类中，存在严重的分类交叉问题：
- **S1:2017 Injection** - 攻击手段
- **S3:2017 Sensitive Data Exposure** - 攻击后果  
- **S8:2017 Insecure Deserialization** - 缺陷类型
- **S9:2017 Using Components with Known Vulnerabilities** - 缺陷类型

**示例**: DVSA应用中S1:Injection的示例攻击同时涉及多个分类：
- 调用了`node_serialize`第三方库（S9: Known Vulnerabilities）
- 存在不安全反序列化（S8: Insecure Deserialization）
- 导致数据泄露（S3: Sensitive Data Exposure）

#### 分类依据混乱
- **攻击手段**: Injection、XXE、XSS
- **缺陷类型**: Insecure Deserialization、Broken Authentication
- **攻击后果**: Sensitive Data Exposure、DoS

**结论**: 只有按缺陷类型分类，才适合讨论"是否适合根因分析"

### 现有框架分类依据分析

| 分类 | 分类依据 |
|------|----------|
| **OWASP S1:2017 Injection** | 攻击手段 |
| **OWASP S2:2017 Broken Authentication** | 缺陷类型 |
| **OWASP S3:2017 Sensitive Data Exposure** | 攻击后果 |
| **OWASP S4:2017 XML External Entities (XXE)** | 攻击手段 |
| **OWASP S5:2017 Broken Access Control** | 缺陷类型 |
| **OWASP S6:2017 Security Misconfiguration** | 缺陷类型 |
| **OWASP S7:2017 Cross-Site Scripting (XSS)** | 攻击手段 |
| **OWASP S8:2017 Insecure Deserialization** | 缺陷类型 |
| **OWASP S9:2017 Using Components with Known Vulnerabilities** | 缺陷类型 |
| **OWASP S10:2017 Insufficient Logging and Monitoring** | 缺陷类型 |

| 分类 | 分类依据 |
|------|----------|
| **Puresec PS1: Function Event Data Injection** | 攻击手段 |
| **Puresec PS2: Broken Authentication** | 缺陷类型 |
| **Puresec PS3: Insecure Serverless Deployment Configuration** | 缺陷类型 |
| **Puresec PS4: Over-Privileged Function Permissions & Roles** | 缺陷类型 |
| **Puresec PS5: Inadequate Function Monitoring and Logging** | 缺陷类型 |
| **Puresec PS6: Insecure 3rd Party Dependencies** | 缺陷类型 |
| **Puresec PS7: Insecure Application Secrets Storage** | 缺陷类型 |
| **Puresec PS8: Denial of Service & Financial Resource Exhaustion** | 攻击后果 |
| **Puresec PS9: Serverless Function Execution Flow Manipulation** | 攻击后果 |
| **Puresec PS10: Improper Exception Handling and Verbose Error Messages** | 缺陷类型 |

### 重构后的缺陷类型分类

#### 配置缺陷（平台层面）
- **函数/容器权限配置缺陷** - 函数权限过度授予
- **存储权限配置缺陷** - 存储桶权限配置错误
- **监控/日志配置缺陷** - 监控和日志配置不当
- **异常处理配置缺陷** - 错误信息泄露配置
- **资源管理控制缺陷(DoS攻击)** - 并发限制、超时配置
- **未隔离开发(dev)版本** - 开发版本暴露

#### 代码缺陷（应用层面）
- **任意代码执行缺陷**（用户输入验证不足）
  - OS命令注入
  - 函数运行时命令注入
  - 反序列化注入
  - SQL/NoSQL命令注入
  - XML外部实体(XEE)
  - 跨站脚本攻击(XSS)
- **竞态条件漏洞(TOCTOE)**
- **路径/地址验证不足缺陷**
- **身份验证缺失(模仿其他用户)**
- **正则表达式验证不足缺陷**

## 🎯 攻击场景真实案例

### 案例汇总表

| 序号 | 攻击分类（末端分类） | 真实案例 |
|------|----------------------|----------|
| 1 | 函数/容器权限配置缺陷 | DVSA-2a、CloudGoat-lambda_privesc、vulnerable_lambda、AWSGoat-4(1) |
| 2 | 存储权限配置缺陷 | DVSA-4,7、ServerlessGoat-4,5 |
| 3 | 监控/日志配置缺陷 | 注：偏重"事后"，未找到具体案例 |
| 4 | 异常处理配置缺陷 | DVSA-10、ServerlessGoat-1 |
| 5 | 资源管理控制缺陷(DoS攻击) | DVSA-6、ServerlessGoat-7 |
| 6 | OS命令注入 | AWS-VL、lambhack、ServerlessGoat-2,3,6、very-vulnerable-serverless-Command Execution |
| 7 | 函数运行时命令注入 | log4shell_serverless |
| 8 | 反序列化注入 | DVSA-1,3,5,9、DVFaaS-Insecure Deserialization、very-vulnerable-serverless-Python deserialization vulnerability |
| 9 | SQL/NoSQL命令注入 | DVFaaS-injection、iam_privilege_escalation、AWSGoat-2(1),1(2) |
| 10 | XML外部实体(XEE) | DVFaaS-XXE |
| 11 | 跨站脚本攻击(XSS) | AWSGoat-1(1)、very-vulnerable-serverless-Injection Vulnerability |
| 12 | 竞态条件漏洞(TOCTOE) | DVSA-8 |
| 13 | 路径/地址验证不足缺陷 | AWSGoat-5,6(1)、very-vulnerable-serverless-SSRF |
| 14 | 身份验证缺失(模仿其他用户) | AWSGoat-3、DVSA-2b |
| 15 | 未隔离开发(dev)版本 | AWSGoat-7(1) |
| 16 | 正则表达式验证不足缺陷 | very-vulnerable-serverless-ReDoS vulnerability |

## 🔍 根因分析适用性评估

### 总体评估表

| 攻击分类（总体分类） | 攻击分类（末端分类） | 适合根因分析？ |
|----------------------|----------------------|----------------|
| **配置缺陷** | 函数/容器权限配置缺陷 | ❌ |
| **配置缺陷** | 存储权限配置缺陷 | ❌ |
| **配置缺陷** | 监控/日志配置缺陷 | ❌ |
| **配置缺陷** | 异常处理配置缺陷 | ❌ |
| **配置缺陷** | 资源管理控制缺陷(DoS攻击) | ❌ |
| **配置缺陷** | 未隔离开发(dev)版本 | ❌ |
| **代码缺陷** | OS命令注入 | ✅ |
| **代码缺陷** | 函数运行时命令注入 | ✅ |
| **代码缺陷** | 反序列化注入 | ✅ |
| **代码缺陷** | SQL/NoSQL命令注入 | ✅ |
| **代码缺陷** | XML外部实体(XEE) | ✅ |
| **代码缺陷** | 跨站脚本攻击(XSS) | ✅ |
| **代码缺陷** | 竞态条件漏洞(TOCTOE) | ❌ |
| **代码缺陷** | 路径/地址验证不足缺陷 | ✅ |
| **代码缺陷** | 身份验证缺失(模仿其他用户) | ❌ |
| **代码缺陷** | 正则表达式验证不足缺陷 | ✅ |

### 详细分析

#### 配置缺陷类（不适合根因分析）

**函数/容器权限配置缺陷**
- **不适合原因**: 权限配置在平台控制台进行，与应用代码无关
- **案例**: DVSA-2a中付费函数无需身份验证即可调用，通过AWS Lambda控制台配置解决

**存储权限配置缺陷**
- **不适合原因**: 存储权限在存储服务配置平台设置，与应用代码无关
- **案例**: DVSA-4中S3存储桶权限配置错误，通过AWS S3控制台配置解决

**监控/日志配置缺陷**
- **不适合原因**: 监控系统应与应用系统独立，缺陷属于监控系统
- **案例**: AWS CloudWatch配置导致日志记录不完整

**异常处理配置缺陷**
- **不适合原因**: API Gateway配置决定错误信息展示，与应用代码无关
- **案例**: DVSA-10中错误信息直接返回用户，通过API Gateway配置解决

**资源管理控制缺陷(DoS攻击)**
- **不适合原因**: 并发限制、超时时间在平台控制台配置
- **案例**: DVSA-6中账单处理函数并发数限制为10，通过AWS控制台配置解决

#### 代码缺陷类（适合根因分析）

**OS命令注入**
- **适合原因**: 使用`system()`、`exec()`等危险函数的行可作为根因代码行
- **案例**: lambhack中`c := exec.Command(command, args...)`行

**函数运行时命令注入**
- **适合原因**: 使用`eval()`、`Function()`等危险函数的行可作为根因代码行
- **案例**: log4shell_serverless中使用不安全的Log4j2库

**反序列化注入**
- **适合原因**: 反序列化函数行可作为根因代码行
- **案例**: DVSA-1中`var req = serialize.unserialize(event.body)`行

**SQL/NoSQL命令注入**
- **适合原因**: 执行用户输入查询语句的代码行可作为根因代码行
- **案例**: DVFaaS-injection中直接拼接SQL语句的代码行

**XML外部实体(XEE)**
- **适合原因**: 解析用户XML的代码行可作为根因代码行
- **案例**: DVFaaS-XXE中`doc = parseString(docbody.decode('utf-8'))`行

**跨站脚本攻击(XSS)**
- **适合原因**: 展示用户输入内容的代码行可作为根因代码行
- **案例**: very-vulnerable-serverless中直接输出用户输入的代码行

**路径/地址验证不足缺陷**
- **适合原因**: 使用用户上传路径/URL的代码行可作为根因代码行
- **案例**: very-vulnerable-serverless-SSRF中URL重定向代码行

**正则表达式验证不足缺陷**
- **适合原因**: 解析正则表达式字符串的代码行可作为根因代码行
- **案例**: very-vulnerable-serverless中ReDoS漏洞相关代码行

#### 代码缺陷类（不适合根因分析）

**竞态条件漏洞(TOCTOE)**
- **不适合原因**: 与整个应用代码结构相关，非特定代码行引起
- **案例**: DVSA-8中商品数量更新竞态条件

**身份验证缺失(模仿其他用户)**
- **不适合原因**: 难以区分攻击者模仿与正常用户操作
- **案例**: DVSA-2b中header验证缺失导致用户身份冒充

## 📚 调研应用清单

| 应用名 | 平台 | 攻击场景 | 注释 |
|--------|------|----------|------|
| **DVSA** | AWS | 1,2,4,5,6,8,12,14 | 攻击多数利用反序列化漏洞 |
| **AWS-Vulnerable-Lambda** | AWS | 6 | 无攻击示例 |
| **lambhack** | AWS | 6 | 攻击示例见repo PPT 127页及之后 |
| **DVFaaS** | AWS | 8,9,10 | 攻击示例见每个子应用文件夹 |
| **ServerlessGoat** | AWS | 1,2,4,5,6 | 攻击编号见LESSONS.md |
| **CloudGoat** | AWS | 1,12 | 多种云服务，含AWS Lambda相关内容 |
| **AWSGoat** | AWS | 1,7,9,11,13,15 | 攻击见attack-manuals，两个不同模块 |
| **very-vulnerable-serverless** | AWS | 6,8,11,13,16 | 无攻击示例 |
| **AzureGoat** | Azure | ... | AWSGoat的Azure平台版本 |
| **log4shell_serverless** | AWS | 7 | 专为Log4j2(CVE-2021-44228)漏洞设计 |

## 🔗 相关后续调研
- **FaaS RCA**: 攻击分类补充调研
- **FaaS**: 控制流攻击调研

## 📖 有状态vs无状态Serverless应用
- **参考资料**: https://milvus.io/ai-quick-reference/what-is-the-difference-between-stateful-and-stateless-serverless-applications