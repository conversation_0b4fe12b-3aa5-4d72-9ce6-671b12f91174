# 实验设计方案

## 实验目标

### 主要研究问题
1. **稳定性假设验证**: "良性行为更稳定"在不同场景下是否成立？
2. **方法有效性**: 提出的变异-稳定性方法检测准确率如何？
3. **适用性边界**: 方法在哪些条件下有效，哪些情况下失效？
4. **性能优势**: 相比现有方法有什么优势和劣势？

### 评估维度
- **准确性**: Precision, Recall, F1-score, AUC
- **稳定性**: 变异回归率, 稳定性系数
- **效率**: 执行时间, 内存占用, 可扩展性
- **鲁棒性**: 不同数据集、参数设置下的表现

## 实验场景设计

### 场景1: 二进制漏洞检测

#### 实验设置
- **目标**: 检测程序是否包含漏洞
- **指标**: crash/non-crash状态
- **变异策略**: 输入数据变异、程序参数调整
- **参考基准**: Racing方法、传统fuzzing

#### 数据集
1. **CVE漏洞程序** (100个)
   - 缓冲区溢出漏洞
   - 格式字符串漏洞  
   - 整数溢出漏洞

2. **正常程序** (100个)
   - GNU核心工具
   - 开源应用程序
   - 系统工具程序

#### 实验步骤
```python
# 伪代码示例
for program in test_programs:
    # 1. 生成变异输入
    mutations = generate_input_mutations(program, mutation_config)
    
    # 2. 执行程序
    crash_results = []
    for mutation in mutations:
        result = execute_program(program, mutation)
        crash_results.append(result.crashed)
    
    # 3. 计算稳定性
    stability_score = calculate_stability(crash_results)
    
    # 4. 分类
    classification = classify_program(stability_score, threshold)
```

### 场景2: Web异常检测

#### 实验设置  
- **目标**: 区分正常请求、新模式、攻击请求
- **指标**: HTTP响应、日志模式
- **变异策略**: 参数替换、载荷扰动
- **参考基准**: Amber方法、WAF规则

#### 数据集
1. **Web应用**
   - DVSA (Damn Vulnerable Serverless Application)
   - WebGoat测试应用
   - 真实Web应用

2. **请求样本**
   - 正常业务请求 (1000个)
   - SQL注入攻击 (500个)
   - XSS攻击 (500个)
   - 新业务模式 (200个)

#### 实验步骤
```python
# 伪代码示例
for request in test_requests:
    # 1. 生成请求变异
    variants = generate_request_variants(request, variant_config)
    
    # 2. 发送请求并收集响应
    responses = []
    for variant in variants:
        response = send_request(variant)
        behavior = extract_behavior(response)
        responses.append(behavior)
    
    # 3. 分析稳定性
    stability = analyze_request_stability(responses)
    
    # 4. 三分类决策
    category = classify_request(stability, thresholds)
```

### 场景3: 日志异常检测

#### 实验设置
- **目标**: 检测系统日志中的异常模式
- **指标**: 日志事件序列
- **变异策略**: 事件扰动、时序调整  
- **参考基准**: DeepLog、LogAnomaly

#### 数据集
1. **公开日志数据集**
   - HDFS日志数据集
   - BGL日志数据集
   - Thunderbird日志数据集

2. **标注数据**
   - 正常日志序列 (80%)
   - 异常日志序列 (20%)

#### 实验步骤
```python
# 伪代码示例  
for log_sequence in test_logs:
    # 1. 生成序列变异
    mutations = mutate_log_sequence(log_sequence, mutation_config)
    
    # 2. 分析序列模式
    patterns = []
    for mutation in mutations:
        pattern = extract_log_pattern(mutation)
        patterns.append(pattern)
    
    # 3. 计算模式稳定性
    stability = compute_pattern_stability(patterns)
    
    # 4. 异常检测
    is_anomaly = detect_log_anomaly(stability, threshold)
```

## 对比方法

### 基准方法
1. **传统规则方法**
   - 基于签名的检测
   - 启发式规则
   - 阈值检测

2. **机器学习方法**
   - SVM分类器
   - 随机森林
   - 神经网络

3. **现有变异方法**
   - AFL fuzzing
   - 传统变异测试
   - 对抗样本生成

4. **领域特定方法**
   - Racing (二进制)
   - Amber (Web)  
   - DeepLog (日志)

### 对比实验设计

#### 性能对比
- 在相同数据集上运行所有方法
- 使用相同的评估指标
- 控制实验条件和参数

#### 效率对比
- 测量执行时间和内存使用
- 分析可扩展性
- 比较资源开销

#### 鲁棒性对比
- 不同数据分布下的表现
- 参数敏感性分析
- 噪声数据的影响

## 评估指标

### 准确性指标

#### 二分类指标
- **Precision**: TP / (TP + FP)
- **Recall**: TP / (TP + FN)  
- **F1-score**: 2 × (Precision × Recall) / (Precision + Recall)
- **Accuracy**: (TP + TN) / (TP + TN + FP + FN)

#### ROC分析
- **AUC**: ROC曲线下面积
- **TPR**: 真正例率
- **FPR**: 假正例率

#### 多分类指标
- **Macro-F1**: 各类别F1的平均值
- **Micro-F1**: 全局微平均F1
- **Confusion Matrix**: 混淆矩阵分析

### 稳定性指标

#### 变异回归率
```python
regression_rate = count(similar_behavior) / total_mutations
```

#### 稳定性系数
```python
stability_coeff = 1 - std(similarity_scores) / mean(similarity_scores)
```

#### 收敛性分析
```python
convergence = measure_convergence(similarity_sequence)
```

### 效率指标

#### 时间复杂度
- **变异生成时间**: 生成变异体的时间
- **执行时间**: 执行所有变异体的时间
- **分析时间**: 稳定性分析的时间

#### 空间复杂度
- **内存占用**: 峰值内存使用量
- **存储需求**: 中间结果存储需求

#### 可扩展性
- **样本数量扩展性**: 大规模样本处理能力
- **并行化效果**: 并行执行的加速比

## 实验实施计划

### 阶段1: 基础实验 (1个月)
- 实现基本的变异-稳定性框架
- 在小规模数据集上验证方法可行性
- 调试和优化核心算法

### 阶段2: 场景实验 (2个月)  
- 实现三个主要场景的具体应用
- 收集和准备实验数据集
- 执行各场景的对比实验

### 阶段3: 深度分析 (1个月)
- 进行消融实验分析
- 参数敏感性研究
- 失效案例分析

### 阶段4: 综合评估 (1个月)
- 整合所有实验结果
- 撰写实验报告
- 准备论文实验部分

## 预期结果

### 定量结果
- 在二进制检测中准确率提升10-15%
- 在Web检测中三分类准确率达到85%+
- 在日志检测中F1-score达到90%+
- 相比基准方法误报率降低20-30%

### 定性分析
- 证实稳定性假设在多数场景下成立
- 识别假设失效的具体条件
- 提供方法改进的方向指导

### 局限性分析
- 高度混淆样本的处理困难
- 计算复杂度相对较高
- 需要领域知识指导变异策略

这个实验设计方案全面覆盖了理论验证、方法评估和应用分析，能够为研究提供可信的实验支撑。