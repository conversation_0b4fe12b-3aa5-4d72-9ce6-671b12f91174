# 扰动-稳定性调研方法论

## 调研策略

师兄的建议非常合理！**"扰动前后产生或不产生变化的场景和工作"** 是理解稳定性假设的关键。这样的调研可以帮助我们：

1. **验证假设的普遍性**: 在多少场景下稳定性假设成立？
2. **识别失效边界**: 什么条件下假设不成立？
3. **优化方法设计**: 如何针对性地改进扰动和分析策略？
4. **避免重复工作**: 充分利用现有研究成果

## 调研框架

### 核心调研问题
```
对于给定的系统S、扰动策略P、行为度量B：
1. 什么情况下: B(S) ≈ B(P(S)) (行为稳定)
2. 什么情况下: B(S) ≠ B(P(S)) (行为敏感)
3. 影响因素: 哪些因素决定稳定性/敏感性？
```

### 调研维度分解

#### 维度1: 按系统类型分类
```
系统分类调研 = {
    确定性系统: {
        特点: 相同输入产生相同输出
        扰动效果: 相对可预测
        代表: 数学计算程序, 编译器
        相关工作: [待调研]
    },
    
    随机性系统: {
        特点: 包含随机因素
        扰动效果: 难以预测  
        代表: 游戏程序, 机器学习模型
        相关工作: [待调研]
    },
    
    交互式系统: {
        特点: 依赖外部输入/环境
        扰动效果: 高度依赖上下文
        代表: Web应用, 操作系统
        相关工作: [待调研]
    },
    
    实时系统: {
        特点: 对时间敏感
        扰动效果: 时间因素影响大
        代表: 嵌入式系统, 流处理
        相关工作: [待调研]
    }
}
```

#### 维度2: 按扰动类型分类
```
扰动分类调研 = {
    输入扰动: {
        数据变异: Racing, AFL fuzzing
        参数调整: Amber的请求变异
        格式变换: 文件格式fuzzing
        编码变化: 字符编码转换
    },
    
    结构扰动: {
        代码变异: 程序变异测试
        请求结构: HTTP请求结构修改
        配置修改: 系统配置参数调整
        协议变异: 网络协议fuzzing
    },
    
    环境扰动: {
        系统状态: 内存/CPU状态变化
        网络条件: 延迟/丢包模拟
        资源限制: 内存/磁盘限制
        并发条件: 多线程/进程干扰
    },
    
    时序扰动: {
        执行时序: 指令重排序
        事件时序: 事件到达顺序
        调度影响: 进程调度变化
        时间窗口: 采样时间窗调整
    }
}
```

#### 维度3: 按行为度量分类  
```
行为度量调研 = {
    执行结果: {
        二值结果: crash/non-crash (Racing)
        状态码: HTTP响应码 (Web测试)
        错误类型: 异常分类
        输出内容: 程序输出比较
    },
    
    执行过程: {
        调用序列: 系统调用trace (strace)
        API序列: API调用模式
        网络流量: 网络包分析
        内存访问: 内存访问模式
    },
    
    性能特征: {
        执行时间: 性能profiling
        资源消耗: CPU/内存使用
        吞吐量: 请求处理速度
        响应时间: 延迟特征
    },
    
    语义特征: {
        功能正确性: 功能测试结果
        业务逻辑: 业务规则验证
        数据完整性: 数据一致性检查
        安全属性: 安全策略遵守
    }
}
```

## 具体调研执行计划

### 第一阶段: 经典工作深度调研 (1周)

#### 必读核心论文
1. **Racing** (`/home/<USER>/yuchen/rca/papers/racing/`)
   - 重点: 强化学习如何利用稳定性进行根因分析
   - 关注: 什么时候变异能让程序从crash回到non-crash

2. **Amber** (`/home/<USER>/yuchen/ServerlessAD/ase25-paper1493-2.pdf`)
   - 重点: Web请求扰动的三分类策略
   - 关注: 什么扰动能区分攻击和正常请求

3. **AFL/LibFuzzer** 
   - 重点: 输入变异对程序行为的影响模式
   - 关注: 覆盖率引导的变异策略

#### 调研记录模板
```markdown
## 论文标题
**基本信息**: 作者、会议/期刊、年份

**核心方法**: 
- 扰动策略: 如何产生变异？
- 行为度量: 如何定义行为？
- 稳定性分析: 如何判断稳定性？

**关键发现**:
- 扰动敏感场景: 什么时候扰动影响大？
- 扰动稳定场景: 什么时候扰动影响小？
- 影响因素: 哪些因素决定敏感性？

**对本研究启发**:
- 支持假设的证据
- 挑战假设的证据  
- 方法改进思路
```

### 第二阶段: 系统性文献调研 (2周)

#### 搜索关键词组合
```
基础关键词:
- perturbation analysis, mutation testing, fuzzing
- behavior stability, robustness analysis  
- anomaly detection, intrusion detection

组合搜索:
- "perturbation" + "stability" + "malware/attack"
- "mutation" + "robustness" + "vulnerability"  
- "fuzzing" + "behavior" + "classification"
- "adversarial" + "robustness" + "security"
```

#### 数据库和会议
```
顶级安全会议: USENIX Security, CCS, NDSS, S&P
系统会议: SOSP, OSDI, ATC, EuroSys  
软件工程: ICSE, FSE, ASE, ISSTA
机器学习: ICML, NeurIPS, ICLR (对抗鲁棒性)
```

### 第三阶段: 实证调研 (1周)

#### 开源工具调研
1. **Fuzzing工具**
   - AFL++, LibFuzzer, Honggfuzz
   - 观察: 什么输入变异容易触发crash？

2. **Web测试工具**  
   - OWASP ZAP, Burp Suite, SQLMap
   - 观察: 什么参数修改能绕过检测？

3. **变异测试工具**
   - PIT (Java), Stryker (JavaScript), MutPy (Python)
   - 观察: 什么代码变异不影响测试结果？

#### 实证实验设计
```python
# 简单实证实验框架
def empirical_study(target_programs, perturbation_strategies):
    results = {}
    for program in target_programs:
        for strategy in perturbation_strategies:
            # 生成扰动
            perturbed_inputs = strategy.generate(program.inputs)
            
            # 执行对比
            original_behavior = execute(program, program.inputs)
            perturbed_behaviors = [execute(program, inp) for inp in perturbed_inputs]
            
            # 计算稳定性
            stability = calculate_stability(original_behavior, perturbed_behaviors)
            results[program.name][strategy.name] = stability
    
    return results
```

## 调研文档结构

### 文件组织
```
/home/<USER>/yuchen/mutate-dis/research/
├── survey-methodology.md        # 本文件
├── literature-review/           # 文献调研
│   ├── core-papers/            # 核心论文深度分析
│   │   ├── racing-analysis.md
│   │   ├── amber-analysis.md
│   │   └── afl-analysis.md  
│   ├── systematic-survey/      # 系统性调研
│   │   ├── perturbation-methods.md
│   │   ├── stability-measures.md
│   │   └── application-domains.md
│   └── summary.md              # 调研总结
├── empirical-study/            # 实证调研
│   ├── tool-analysis/          # 工具分析
│   ├── case-studies/           # 案例研究
│   └── findings.md             # 实证发现
└── synthesis.md                # 综合分析
```

### 每日调研任务

#### 第1-2天: Racing深度分析
- 精读Racing论文
- 理解强化学习如何利用程序稳定性
- 分析在什么条件下变异能帮助根因定位

#### 第3-4天: Amber深度分析  
- 精读Amber论文 (参考`/home/<USER>/yuchen/ServerlessAD/`)
- 理解Web请求扰动的机制
- 分析三分类(良性/新模式/攻击)的依据

#### 第5-7天: 扩展文献调研
- 系统性搜索相关工作
- 按维度分类整理文献
- 识别研究空白和机会

## 立即开始的具体步骤

### 今天就可以执行的任务:

1. **重读Racing论文** (1-2小时)
   - 文件位置: `/home/<USER>/yuchen/rca/papers/racing/README.md`
   - 重点关注: 第4节的强化学习算法如何利用变异稳定性

2. **重读Amber论文** (1-2小时)  
   - 文件位置: `/home/<USER>/yuchen/ServerlessAD/ase25-paper1493-2.pdf`
   - 重点关注: 扰动策略设计和三分类机制

3. **开始文献搜索** (1小时)
   - 在Google Scholar搜索关键词组合
   - 收集近3年的相关高质量论文
   - 建立论文管理列表

这个调研方法论为您提供了系统性的调研路径，可以帮助您全面理解扰动-稳定性的研究现状，为后续的方法设计奠定坚实基础。