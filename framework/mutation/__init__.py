"""
变异引擎模块

实现多种变异策略：
1. 语法保持变异 (Syntax-Preserving)
2. 语义保持变异 (Semantic-Preserving)  
3. 对抗性变异 (Adversarial)
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
import random
import copy

class BaseMutator(ABC):
    """变异器基类"""
    
    @abstractmethod
    def mutate(self, sample: Any, config: Dict) -> List[Any]:
        """
        执行变异操作
        
        Args:
            sample: 原始样本
            config: 变异配置
            
        Returns:
            变异体列表
        """
        pass
    
    @abstractmethod  
    def get_mutation_type(self) -> str:
        """获取变异类型"""
        pass
    
    def validate_mutation(self, original: Any, mutated: Any) -> bool:
        """验证变异是否有效"""
        return mutated is not None

class SyntaxPreservingMutator(BaseMutator):
    """语法保持变异器"""
    
    def __init__(self, grammar_rules: Dict = None):
        self.grammar_rules = grammar_rules or {}
        
    def mutate(self, sample: Any, config: Dict) -> List[Any]:
        """保持语法正确性的变异"""
        variants = []
        mutation_count = config.get('mutation_count', 10)
        
        for _ in range(mutation_count):
            variant = self._syntax_preserving_mutate(sample, config)
            if self.validate_mutation(sample, variant):
                variants.append(variant)
                
        return variants
    
    def _syntax_preserving_mutate(self, sample: Any, config: Dict) -> Any:
        """具体的语法保持变异逻辑"""
        # 这里需要根据具体的样本类型实现
        # 例如：程序输入、网络请求、日志条目等
        raise NotImplementedError("需要子类实现具体的变异逻辑")
    
    def get_mutation_type(self) -> str:
        return "syntax_preserving"

class SemanticPreservingMutator(BaseMutator):
    """语义保持变异器"""
    
    def __init__(self, semantic_rules: Dict = None):
        self.semantic_rules = semantic_rules or {}
        self.equivalent_mappings = {}
        
    def mutate(self, sample: Any, config: Dict) -> List[Any]:
        """保持语义的变异"""
        variants = []
        
        # 等价参数替换
        equiv_variants = self._equivalent_substitution(sample, config)
        variants.extend(equiv_variants)
        
        # 功能保持变异
        func_variants = self._functional_preserving_mutate(sample, config)
        variants.extend(func_variants)
        
        return variants
    
    def _equivalent_substitution(self, sample: Any, config: Dict) -> List[Any]:
        """等价参数替换"""
        raise NotImplementedError("需要子类实现")
    
    def _functional_preserving_mutate(self, sample: Any, config: Dict) -> List[Any]:
        """功能保持变异"""  
        raise NotImplementedError("需要子类实现")
    
    def get_mutation_type(self) -> str:
        return "semantic_preserving"

class AdversarialMutator(BaseMutator):
    """对抗性变异器"""
    
    def __init__(self, attack_patterns: Dict = None):
        self.attack_patterns = attack_patterns or {}
        self.disruption_strategies = {}
        
    def mutate(self, sample: Any, config: Dict) -> List[Any]:
        """对抗性变异，专门破坏攻击载荷"""
        variants = []
        
        # 攻击特征破坏
        attack_disrupted = self._disrupt_attack_features(sample, config)
        variants.extend(attack_disrupted)
        
        # 编码破坏
        encoding_disrupted = self._disrupt_encoding(sample, config)
        variants.extend(encoding_disrupted)
        
        return variants
    
    def _disrupt_attack_features(self, sample: Any, config: Dict) -> List[Any]:
        """破坏攻击特征"""
        raise NotImplementedError("需要子类实现")
    
    def _disrupt_encoding(self, sample: Any, config: Dict) -> List[Any]:
        """破坏编码方式"""
        raise NotImplementedError("需要子类实现")
    
    def get_mutation_type(self) -> str:
        return "adversarial"

class MutationEngine:
    """变异引擎管理器"""
    
    def __init__(self):
        self.mutators = {}
        
    def register_mutator(self, name: str, mutator: BaseMutator):
        """注册变异器"""
        self.mutators[name] = mutator
        
    def generate_variants(self, sample: Any, config: Dict) -> Dict[str, List[Any]]:
        """生成所有类型的变异体"""
        variants = {}
        enabled_mutators = config.get('enabled_mutators', list(self.mutators.keys()))
        
        for mutator_name in enabled_mutators:
            if mutator_name in self.mutators:
                mutator = self.mutators[mutator_name]
                mutator_config = config.get(mutator_name, {})
                variants[mutator_name] = mutator.mutate(sample, mutator_config)
                
        return variants
    
    def get_total_variants_count(self, variants: Dict[str, List[Any]]) -> int:
        """获取变异体总数"""
        return sum(len(variant_list) for variant_list in variants.values())