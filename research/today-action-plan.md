# 变异-稳定性研究调研行动计划

## 核心研究范围
**不限于异常检测**: 包括漏洞挖掘(fuzz)、恶意软件检测、Web安全、系统入侵检测等所有基于行为分析的安全应用

## 立即执行任务清单

### 任务1: Racing论文深度分析 (60分钟) ✋ **现在开始**

#### 分析重点
1. **变异在漏洞根因分析中的作用机制** (20分钟)
   - Racing如何通过变异让程序从crash回到non-crash？
   - 强化学习如何利用"良性程序更容易回归"这一直觉？
   - 这种方法在什么类型的漏洞上有效/无效？

2. **变异策略与程序/漏洞类型的匹配** (20分钟)  
   - Racing针对什么类型的程序和漏洞？
   - 指令级变异如何设计以利用稳定性差异？
   - 不同变异策略对不同漏洞类型的效果差异？

3. **稳定性假设的具体体现** (20分钟)
   - Racing中如何定义和度量程序行为稳定性？
   - 反例生成过程如何体现"良性更稳定"？
   - 在什么边界条件下假设可能失效？

#### 记录模板
```markdown
## Racing关键发现

### 支持稳定性假设的证据:
- [ ] 具体证据1...
- [ ] 具体证据2...

### 挑战稳定性假设的情况:
- [ ] 失效场景1...
- [ ] 失效场景2...

### 方法细节:
- 变异策略: ...
- 稳定性度量: ...
- 适用条件: ...

### 对本研究的启发:
- [ ] 可借鉴的方法...
- [ ] 需要改进的地方...
```

---

### 任务2: Amber论文深度对比分析 (60分钟)

#### 分析重点
1. **Web场景下的稳定性假设体现** (20分钟)
   - Amber如何在Web请求中体现"良性更稳定"？
   - 三分类(正常请求/新模式/攻击请求)如何反映稳定性差异？
   - 与Racing的crash/non-crash二分类相比，复杂度提升在哪里？

2. **变异策略的跨领域对比** (20分钟)
   - Amber的参数替换 vs Racing的指令变异：相同点和差异点？
   - 两者如何针对各自领域的特点设计变异策略？
   - 变异粒度(指令 vs 参数)对稳定性分析的影响？

3. **行为度量的适用性对比** (20分钟)
   - Racing: crash状态 vs Amber: 日志模式 + HTTP响应
   - 两种行为度量对稳定性假设的适用性在哪里？
   - rc(回归系数) vs cv(变异系数)如何量化稳定性？

#### 参考文件
- 主论文: `/home/<USER>/yuchen/ServerlessAD/ase25-paper1493-2.pdf`
- 实现总结: `/home/<USER>/yuchen/ServerlessAD/IMPLEMENTATION_SUMMARY.md`
- 技术文档: `/home/<USER>/yuchen/ServerlessAD/algorithms/addtb/README.md`

---

### 任务3: 系统性文献初步调研 (30分钟)

#### 搜索策略设计 - 专门针对扰动-稳定性调研

##### 第一层：核心概念搜索模式
**模式1: 直接稳定性概念**
```
主关键词: [stability, robustness, resilience, invariance]
修饰词: [behavioral, execution, functional, semantic]
领域词: [software, program, application, system]

组合模式:
- "behavioral stability" + [mutation, perturbation, variation]
- "execution robustness" + [testing, analysis, evaluation] 
- "functional invariance" + [under, against] + [input changes, modifications]
- "semantic preservation" + [mutation, transformation]
```

**模式2: 变化vs不变化对比**
```
对比概念: [sensitive vs robust, fragile vs stable, brittle vs resilient]

具体搜索词:
- "sensitive to perturbation" vs "robust to perturbation"
- "fragile under mutation" vs "stable under mutation"
- "behavior preservation" + "input variation"
- "invariant properties" + "program mutation"
```

##### 第二层：应用场景特定搜索

**场景1: 二进制程序分析** (10分钟)
```
核心模式:
- "program mutation" + "crash analysis" + [stability, robustness]
- "input fuzzing" + "behavior consistency"
- "vulnerability" + "mutation" + "resilience"
- "fault injection" + "program stability"

具体搜索组合:
- "benign program" + "mutation robust*"
- "vulnerable program" + "mutation sensitiv*" 
- "crash avoidance" + "input perturbation"
- "execution path" + "stability analysis"
```

**场景2: Web应用安全** (10分钟)  
```
核心模式:
- "web attack" + "parameter mutation" + [detection, resilience]
- "payload" + "perturbation" + [stability, robustness]
- "request transformation" + "behavior analysis"

具体搜索组合:
- "SQL injection" + "mutation" + "evasion"
- "attack payload" + "stability" + "analysis"
- "legitimate request" + "parameter variation"
- "WAF evasion" + "input transformation"
```

**场景3: 协议和网络安全** (10分钟)
```
核心模式:
- "protocol fuzzing" + "state consistency"
- "network mutation" + "behavior analysis"
- "packet perturbation" + "protocol stability"

具体搜索组合:
- "protocol implementation" + "mutation testing"
- "network protocol" + "robustness analysis"
- "packet modification" + "behavior impact"
```

##### 第三层：方法论搜索模式

**模式3: 测试和分析方法**
```
方法关键词:
- "mutation testing" + [stability, robustness, invariant]
- "perturbation analysis" + [software, security, program]
- "sensitivity analysis" + [input, parameter, behavior]
- "robustness testing" + [mutation, variation, perturbation]

组合搜索:
- "differential testing" + "mutation"
- "metamorphic testing" + "stability"
- "property-based testing" + "perturbation"
```

**模式4: 理论基础搜索**
```
理论概念:
- "program equivalence" + "mutation"
- "behavioral semantics" + "perturbation"
- "execution model" + "stability"
- "formal verification" + "mutation testing"

搜索组合:
- "semantic equivalence" + "under mutation"
- "execution trace" + "similarity" + "mutation"
- "program synthesis" + "robustness"
```

#### 高级搜索技巧

**布尔搜索模式**:
```
模式A: ("mutation" OR "perturbation" OR "variation") AND ("stability" OR "robustness")
模式B: ("benign" OR "legitimate") AND ("malicious" OR "attack") AND "mutation"
模式C: ("behavior" OR "execution") AND "preserv*" AND "perturbation"
```

**否定搜索(排除不相关)**:
```
-"gene mutation" -"biological" -"DNA" -"medical"
-"hardware mutation" -"radiation" -"physical"
-"evolutionary algorithm" (除非与程序进化相关)
```

**特殊关注的研究问题搜索**:
```
1. "什么时候变异影响大？"
   - "mutation impact" + "factor*"
   - "perturbation effect" + "predict*"
   - "sensitivity" + "analysis" + "program"

2. "什么时候变异影响小？"
   - "mutation neutral*" 
   - "perturbation toleran*"
   - "robust* under mutation"

3. "如何预测变异效果？"
   - "mutation effect" + "predict*"
   - "perturbation impact" + "model*"
```

#### 记录要求
- 论文标题、作者、会议/年份
- 一句话总结核心方法
- 与本研究的相关性评分(1-5)

---

### 任务4: 当日总结和明日计划 (30分钟)

#### 总结内容
1. **关键发现汇总**
   - Racing和Amber中稳定性假设的具体体现
   - 支持和挑战假设的证据
   - 方法设计的启发

2. **研究问题细化**
   - 基于今日调研，进一步明确研究边界
   - 识别需要深入调研的具体方向
   - 更新问题定义文档

3. **明日计划制定**
   - 确定明天的具体调研任务
   - 调整调研重点和时间分配

## 文件管理

### 今日产出文件位置
```
/home/<USER>/yuchen/mutate-dis/research/literature-review/core-papers/
├── racing-analysis.md          # 任务1产出
├── amber-analysis.md           # 任务2产出
└── initial-survey.md           # 任务3产出

/home/<USER>/yuchen/mutate-dis/research/
└── daily-progress-[DATE].md    # 任务4产出
```

### 工作流程
1. 每个任务开始前，创建对应的.md文件
2. 边阅读边记录，使用上面的模板
3. 每个任务结束后，更新进度文档
4. 全天结束后，汇总到daily-progress文件

## 时间管理提醒

- ⏰ **严格控制时间**: 每个任务按时间限制执行
- 📝 **边读边记**: 不要等读完再记录，边读边写
- 🎯 **聚焦重点**: 重点关注与稳定性假设相关的内容
- ✅ **完成优于完美**: 在规定时间内产出可用的分析结果

## 紧急联系

如果遇到以下情况，立即停下来重新规划：
- 某个任务超时50%以上
- 发现重要的相关工作需要深入分析
- 对研究方向产生重大疑问

**现在就开始任务1！** 🚀