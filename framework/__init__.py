"""
变异-稳定性异常检测框架

这个框架实现了基于"良性行为更稳定"假设的异常检测方法。
通过对样本进行变异并分析行为稳定性来区分良性和恶意行为。

主要组件:
- mutation: 变异引擎，实现多种变异策略
- execution: 执行引擎，支持多种执行环境  
- analysis: 分析引擎，计算稳定性和相似度
- classification: 分类引擎，基于稳定性进行分类决策

使用示例:
    from mutate_stability import MutationStabilityFramework
    
    framework = MutationStabilityFramework()
    result = framework.detect_anomaly(sample, config)
"""

from .core import (
    BaseMutator,
    BaseExecutor, 
    BaseAnalyzer,
    BaseClassifier,
    MutationStabilityFramework
)

from .mutation import (
    SyntaxPreservingMutator,
    SemanticPreservingMutator,
    AdversarialMutator
)

from .execution import (
    BinaryExecutor,
    WebRequestExecutor,
    LogProcessor
)

from .analysis import (
    SimilarityAnalyzer,
    StabilityAnalyzer
)

from .classification import (
    ThresholdClassifier,
    MLClassifier
)

__version__ = "0.1.0"
__author__ = "Research Team"

__all__ = [
    "MutationStabilityFramework",
    "BaseMutator", "BaseExecutor", "BaseAnalyzer", "BaseClassifier",
    "SyntaxPreservingMutator", "SemanticPreservingMutator", "AdversarialMutator",
    "BinaryExecutor", "WebRequestExecutor", "LogProcessor", 
    "SimilarityAnalyzer", "StabilityAnalyzer",
    "ThresholdClassifier", "MLClassifier"
]