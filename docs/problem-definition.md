# 研究问题精确定义

## 核心研究假设的严谨表述

### 原始直觉
"良性行为更容易回归/更稳定"

### 需要精确定义的要素

#### 1. "良性行为" vs "恶意行为"
**问题**: 如何严格区分良性和恶意？
- **二分类场景**: 良性程序 vs 包含漏洞的程序
- **多分类场景**: 正常请求 vs 新模式 vs 攻击请求
- **连续场景**: 行为的恶意程度是否存在连续性？

**需要明确**:
- 良性/恶意的判断标准
- 边界情况的处理方式
- 标注数据的可靠性

#### 2. "扰动/变异"的定义域
**问题**: 什么样的扰动是有意义的？
- **输入层扰动**: 修改程序输入、请求参数
- **结构层扰动**: 修改程序指令、请求结构
- **环境层扰动**: 修改执行环境、系统状态

**需要明确**:
- 扰动的合理边界
- 扰动强度的量化方法
- 不同扰动类型的适用场景

#### 3. "行为"的具体含义
**问题**: 如何定义和度量"行为"？
- **执行结果**: crash/non-crash, HTTP状态码
- **执行过程**: 系统调用序列, 网络流量
- **状态变化**: 内存状态, 文件系统变化
- **输出特征**: 日志模式, 响应内容

**需要明确**:
- 行为特征的提取方法
- 行为表示的粒度选择
- 行为比较的度量标准

#### 4. "稳定性"的数学定义
**问题**: 如何量化稳定性？
- **相似度函数**: 如何计算行为相似度？
- **聚合方法**: 多次扰动结果如何聚合？
- **阈值设定**: 稳定与不稳定的边界在哪里？

**需要明确**:
- 稳定性的数学表达式
- 稳定性度量的统计特性
- 不同场景下稳定性的可比性

## 研究范围的系统化分解

### 维度1: 应用领域
```
研究范围 = {
    漏洞挖掘: {
        目标: 区分正常程序 vs 包含漏洞程序
        指标: crash/non-crash状态
        扰动: 输入数据变异, 程序参数调整
        代表工作: Racing, AFL fuzzing
    },
    
    Web异常检测: {
        目标: 区分正常请求 vs 新模式 vs 攻击请求  
        指标: HTTP响应, 服务器日志
        扰动: 参数替换, 载荷修改
        代表工作: Amber, WAF系统
    },
    
    系统监控: {
        目标: 区分正常行为 vs 异常行为
        指标: 系统调用, 网络流量, 资源使用
        扰动: 执行环境变化, 输入注入
        代表工作: Sysdig, HIDS
    },
    
    日志分析: {
        目标: 区分正常日志模式 vs 异常模式
        指标: 日志事件序列, 时间模式
        扰动: 事件顺序调整, 参数修改
        代表工作: DeepLog, LogAnomaly
    }
}
```

### 维度2: 技术栈
```
技术场景 = {
    程序语言: [C/C++, Java, Python, JavaScript, Go, ...],
    执行环境: [本地执行, 容器, 虚拟机, 云平台],
    输入类型: [文件输入, 网络输入, 用户交互, API调用],
    输出形式: [程序输出, 系统状态, 网络响应, 日志记录]
}
```

### 维度3: 数据结构
```
行为表示 = {
    序列数据: {
        系统调用序列, API调用序列, 网络包序列,
        日志事件序列, 状态转换序列
    },
    
    图结构: {
        调用图, 依赖图, 溯源图, 
        控制流图, 数据流图
    },
    
    向量特征: {
        统计特征, 频率特征, 时间特征,
        内容特征, 结构特征
    },
    
    复合结构: {
        时间序列 + 图结构,
        多层次特征组合,
        多模态数据融合
    }
}
```

## 具体研究课题的界定

### 核心研究问题
> **在什么条件下，通过扰动分析可以有效区分良性和恶意行为？稳定性假设的适用边界在哪里？**

### 子研究问题
1. **假设验证**: 稳定性假设在哪些场景下成立？失效的条件是什么？
2. **方法设计**: 如何设计有效的扰动策略和稳定性度量方法？
3. **边界分析**: 方法的适用边界和局限性在哪里？
4. **实用价值**: 相比现有方法能带来多大的性能提升？

### 研究范围的优先级
```
Priority 1 (核心场景):
- 二进制漏洞检测 (基于Racing)
- Web攻击检测 (基于Amber)

Priority 2 (扩展场景):  
- 日志异常检测
- 系统行为分析

Priority 3 (探索场景):
- 移动应用分析
- IoT设备检测
```

## 下一步的调研重点

根据师兄建议，重点调研"扰动前后产生或不产生变化的场景和工作"，这确实是核心问题。

### 调研维度
1. **产生变化的场景**: 什么时候扰动会显著改变行为？
2. **不产生变化的场景**: 什么时候扰动对行为影响很小？
3. **变化的性质**: 变化是线性的还是突变的？是可预测的还是随机的？
4. **影响因素**: 哪些因素决定了扰动的效果？

这个精确定义为后续的系统性调研提供了明确的方向和边界。