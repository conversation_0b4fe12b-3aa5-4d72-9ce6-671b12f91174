# Serverless/FaaS攻击全景总结

## 🎯 执行摘要

本总结系统整合了rca/feishu目录下所有以"faas"开头的Markdown文件中的攻击研究，基于缺陷类型而非攻击手段进行分类，提供了Serverless/FaaS攻击的完整视图。

## 📊 攻击分类体系

### 一级分类：配置缺陷 vs 代码缺陷

#### 🔧 配置缺陷（平台层面 - 不适合根因分析）
| 缺陷类型 | 典型案例 | 根因定位 | 修复方式 |
|----------|----------|----------|----------|
| **函数/容器权限配置缺陷** | DVSA-2a支付函数公开访问 | AWS Lambda控制台配置 | 权限策略调整 |
| **存储权限配置缺陷** | S3存储桶公开写权限 | 存储服务配置平台 | 存储桶策略配置 |
| **监控/日志配置缺陷** | CloudWatch配置遗漏 | 监控平台配置 | 日志策略优化 |
| **异常处理配置缺陷** | API Gateway错误信息泄露 | API Gateway配置 | 错误响应配置 |
| **资源管理控制缺陷** | 并发限制设置不当 | 平台资源限制配置 | 并发/超时参数调整 |
| **未隔离开发版本** | dev环境暴露 | 部署环境配置 | 环境隔离配置 |

#### 💻 代码缺陷（应用层面 - 适合根因分析）
| 缺陷类型 | 根因代码特征 | 典型案例 | 攻击向量 |
|----------|--------------|----------|----------|
| **OS命令注入** | `system()`, `exec()`直接执行用户输入 | lambhack | 命令字符串注入 |
| **函数运行时命令注入** | `eval()`, `Function()`执行动态代码 | Log4j2漏洞 | JNDI注入 |
| **反序列化注入** | 不安全反序列化函数 | DVSA-1,3,5,9 | JSON/对象注入 |
| **SQL/NoSQL注入** | 直接拼接查询语句 | DVFaaS-injection | 查询语句注入 |
| **XML外部实体(XEE)** | 未禁用外部实体的XML解析 | DVFaaS-XXE | 恶意XML实体 |
| **跨站脚本攻击(XSS)** | 未转义的用户输入输出 | very-vulnerable-serverless | 脚本注入 |
| **路径/地址验证不足** | 直接使用用户提供的URL/路径 | AWSGoat-5,6 | SSRF/目录遍历 |
| **正则表达式验证不足** | 灾难性回溯的正则 | very-vulnerable-serverless | ReDoS攻击 |

## 🎯 攻击场景矩阵

### 按平台分布
| 平台 | 涉及应用 | 主要攻击类型 |
|------|----------|--------------|
| **AWS** | DVSA, ServerlessGoat, AWSGoat, CloudGoat | 反序列化、权限配置、注入 |
| **Azure** | AzureGoat | 与AWSGoat类似 |
| **通用** | very-vulnerable-serverless | 多种代码缺陷 |

### 按严重程度
| 严重程度 | 攻击类型 | 影响范围 | 检测难度 |
|----------|----------|----------|----------|
| **高危** | 反序列化注入、权限配置缺陷 | 远程代码执行、数据泄露 | 中-高 |
| **中危** | SQL注入、XSS、路径遍历 | 数据篡改、会话劫持 | 中 |
| **低危** | 信息泄露、ReDoS | 系统信息暴露、拒绝服务 | 低-中 |

## 🔍 关键发现

### 1. 攻击模式特征
- **反序列化攻击**是Serverless环境中最普遍的代码缺陷类型
- **权限配置错误**是平台层面最主要的配置缺陷
- **第三方库漏洞**（如node-serialize、Log4j2）是重要攻击来源

### 2. 根因分析可行性
- **配置缺陷**: 100%不适合根因分析（平台层面问题）
- **代码缺陷**: 87.5%适合根因分析（除竞态条件和身份验证缺失外）
- **可定位根因**: 8种代码缺陷类型可精确定位到具体代码行

### 3. 攻击链分析
```
攻击者 → [入口点] → [缺陷触发] → [权限滥用] → [目标系统]
          ↓          ↓         ↓         ↓
        事件注入   反序列化   函数调用   数据泄露
        API调用    代码执行   横向移动   系统控制
```

## 📋 防御策略建议

### 配置层面
1. **最小权限原则**: 函数、存储、网络权限最小化
2. **环境隔离**: 开发、测试、生产环境严格分离
3. **监控告警**: 异常行为实时监控和告警
4. **资源限制**: 合理设置并发、超时、内存限制

### 代码层面
1. **输入验证**: 所有用户输入严格验证和过滤
2. **安全库使用**: 避免使用已知漏洞的第三方库
3. **代码审计**: 重点检查反序列化、命令执行、SQL查询等高危操作
4. **输出编码**: 用户输出内容适当编码和转义

## 🎯 检测与响应

### 检测指标
- **异常函数调用**: 未授权的函数间调用
- **异常数据访问**: 大量数据读取或异常数据模式
- **执行时间异常**: 函数执行时间显著增加
- **错误率飙升**: 特定函数错误率异常升高

### 响应机制
- **自动隔离**: 检测到攻击时自动隔离相关函数
- **权限回收**: 动态调整可疑函数的权限
- **日志分析**: 深度分析攻击路径和影响范围
- **修复验证**: 修复后验证根因代码行的安全性

## 📊 攻击统计总结

### 按类型统计
| 攻击类型 | 出现频率 | 主要影响 | 检测难度 | 防御优先级 |
|----------|----------|----------|----------|------------|
| 配置缺陷 | 高 | 权限滥用 | 低 | 高 |
| 代码缺陷 | 高 | 代码执行 | 中 | 高 |
| 第三方库漏洞 | 中 | 供应链风险 | 高 | 中 |
| 控制流操控 | 低 | 逻辑绕过 | 中 | 中 |

### 新型攻击分类体系
基于攻击路径的Serverless攻击矩阵：

#### 1. 事件驱动攻击面
- 事件源污染攻击
- 事件数据解析攻击

#### 2. 函数生命周期攻击面
- 冷启动攻击
- 执行时攻击

#### 3. 权限配置攻击面
- IAM权限利用
- 资源访问控制绕过

#### 4. 依赖供应链攻击面
- 第三方库攻击
- 容器镜像攻击

#### 5. 网络攻击面
- VPC配置攻击
- API调用链攻击

#### 6. 监控与日志攻击面
- 日志注入攻击
- 监控指标操控

#### 7. 云原生特定攻击
- 容器运行时攻击
- Kubernetes特定攻击

#### 8. 计费与资源攻击
- 计费欺诈攻击
- 资源竞争攻击

### 攻击场景矩阵
| 攻击路径 | 触发条件 | 影响范围 | 检测难度 | 防御优先级 |
|---------|----------|----------|----------|------------|
| 事件源污染 | 外部输入可控 | 单个函数 | 低 | 高 |
| 权限滥用 | IAM配置错误 | 整个账户 | 中 | 高 |
| 供应链投毒 | 依赖更新 | 所有使用函数 | 高 | 中 |
| 容器逃逸 | 运行时漏洞 | 底层基础设施 | 高 | 高 |
| 计费欺诈 | API访问权限 | 财务成本 | 低 | 中 |

### 防御策略建议

#### 1. 架构层面防御
- 零信任架构
- 最小权限原则
- 网络隔离

#### 2. 运行时防御
- 函数沙箱强化
- 资源限制
- 实时监控

#### 3. 供应链安全
- 依赖扫描
- 镜像签名
- 私有仓库

#### 4. 事件验证
- 事件schema验证
- 签名验证
- 频率限制

### 检测与响应框架

#### 1. 检测指标
- 异常调用模式
- 权限使用异常
- 资源使用异常

#### 2. 响应机制
- 自动隔离
- 访问撤销
- 事件溯源

### 按平台统计
- **AWS**: 8个脆弱应用，覆盖所有攻击类型
- **Azure**: 1个脆弱应用，攻击类型与AWS类似
- **跨平台**: 攻击模式具有高度一致性

## 🔗 关联资源

### 关键脆弱应用
- **DVSA**: 最全面的Serverless攻击演示
- **ServerlessGoat**: OWASP官方Serverless安全学习平台
- **AWSGoat**: AWS特定场景的脆弱应用
- **very-vulnerable-serverless**: 通用代码缺陷集合

### 监控工具
- **AWS CloudWatch**: 日志和监控
- **Puresec Serverless Security**: Serverless专用安全工具
- **OWASP ZAP**: Web应用安全测试

---

*本总结基于rca/feishu目录下所有faas-*文件的系统分析，确保无重复、无遗漏地覆盖所有记录的Serverless攻击场景。*